/* Custom CSS for Piata.ro */

/* Primary color scheme */
:root {
    --primary: #0056b3;
    --primary-dark: #004085;
    --primary-light: #4d94ff;
    --primary-lighter: #80b3ff;
    --secondary: #6c757d;
    --accent: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #17a2b8;
}

/* Custom Tailwind-like utilities */
.text-primary { color: var(--primary) !important; }
.text-primary-dark { color: var(--primary-dark) !important; }
.text-primary-light { color: var(--primary-light) !important; }
.text-primary-lighter { color: var(--primary-lighter) !important; }
.bg-primary { background-color: var(--primary) !important; }
.bg-primary-dark { background-color: var(--primary-dark) !important; }
.border-primary { border-color: var(--primary) !important; }
.hover\:bg-primary-dark:hover { background-color: var(--primary-dark) !important; }
.hover\:text-primary-dark:hover { color: var(--primary-dark) !important; }
.hover\:border-primary:hover { border-color: var(--primary) !important; }
.focus\:ring-primary:focus { 
    --tw-ring-color: var(--primary) !important;
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1) !important;
}
.focus\:border-primary:focus { border-color: var(--primary) !important; }

/* Enhanced form styling */
.form-input {
    appearance: none;
    position: relative;
    display: block;
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
    color: #1f2937;
    background-color: #ffffff;
    transition: all 0.2s ease-in-out;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

/* Button enhancements */
.btn-primary {
    background-color: var(--primary);
    color: white;
    border: 2px solid var(--primary);
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    text-decoration: none;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 86, 179, 0.2);
}

.btn-secondary {
    background-color: white;
    color: #374151;
    border: 2px solid #d1d5db;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    text-decoration: none;
}

.btn-secondary:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
}

/* Google button styling */
.btn-google {
    background-color: white;
    color: #374151;
    border: 2px solid #d1d5db;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    text-decoration: none;
    width: 100%;
}

.btn-google:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Card styling */
.card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
}

.card-elevated {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Gradient backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-blue {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

/* Text animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive utilities */
@media (max-width: 640px) {
    .card {
        border-radius: 8px;
        margin: 16px;
    }
}

/* Error states */
.form-error {
    border-color: var(--danger) !important;
}

.form-error:focus {
    border-color: var(--danger) !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
}
