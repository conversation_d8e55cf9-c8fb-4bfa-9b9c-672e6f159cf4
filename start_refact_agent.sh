#!/bin/bash

# Start the Refact MCP Server for AI-powered code refactoring
# This server provides tools for code analysis, refactoring, and optimization

echo "Starting Refact MCP Server..."
echo "Server: AI-powered code refactoring tools"
echo "Port: 8004"
echo "Tools: analyze_code, get_refactoring_suggestions, apply_refactoring, detect_code_smells, optimize_imports"

# Set Python path to include current directory
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Start the MCP server
python ai_assistant/mcp_refact_server.py

# Keep the script running
wait
