
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Color Variables */
:root {
  /* Primary Colors */
  --color-primary-50: 239 246 255;
  --color-primary-100: 219 234 254;
  --color-primary-500: 59 130 246;
  --color-primary-900: 30 58 138;

  /* Neutral Colors */
  --color-gray-100: 243 244 246;
  --color-gray-900: 17 24 39;

  /* Semantic Colors */
  --color-success: 16 185 129;
  --color-warning: 245 158 11;
  --color-danger: 239 68 68;
}

.dark {
  --color-primary-50: 30 41 59;
  --color-primary-100: 51 65 85;
  --color-primary-500: 96 165 250;
  --color-primary-900: 191 219 254;

  --color-gray-100: 31 41 55;
  --color-gray-900: 249 250 251;
}

/* Base Styles */
@layer base {
  html {
    @apply scroll-smooth;
    font-size: 16px;
  }

  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-50;
    font-family: 'Inter var', system-ui, sans-serif;
    line-height: 1.5;
  }

  h1 {
    @apply text-4xl font-bold tracking-tight;
  }
  h2 {
    @apply text-3xl font-semibold;
  }
  h3 {
    @apply text-2xl font-medium;
  }
}

/* Component Styles */
@layer components {
  /* Buttons */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium 
           focus:outline-none focus:ring-2 focus:ring-offset-2 
           transition-colors duration-200;
  }
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white 
           focus:ring-primary-500 px-6 py-3;
  }
  
  /* Cards */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-md 
           overflow-hidden border border-gray-200 dark:border-gray-700;
  }

  /* Forms */
  .input {
    @apply w-full px-4 py-2 border border-gray-300 dark:border-gray-600 
           rounded-lg focus:ring-2 focus:ring-primary-500 
           dark:bg-gray-700 dark:text-white;
  }
}

/* Utility Extensions */
@layer utilities {
  /* Animation */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
  }
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* Layout */
  .container-padded {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }
}
