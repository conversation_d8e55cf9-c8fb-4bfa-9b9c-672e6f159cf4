framework: praisonai
topic: Autonomous Marketplace Monitoring and Analytics

roles:
  market_monitor:
    role: Autonomous Market Monitoring Agent
    backstory: |
      You are an autonomous agent that continuously monitors marketplace activity,
      identifies trends, detects anomalies, and adapts strategies based on real-time feedback.
    goal: |
      Provide real-time market intelligence through autonomous monitoring and
      adaptive analytics with environment feedback loops.
    tasks:
      autonomous_monitoring_task:
        description: |
          Continuously monitor marketplace metrics, detect pattern changes,
          identify emerging trends, and adapt analysis strategies based on environmental feedback.
        expected_output: |
          Real-time market intelligence with trend detection and adaptive insights.

  trend_analyzer:
    role: Market Trend Analysis Specialist
    backstory: |
      You specialize in identifying market patterns, seasonal variations, demand fluctuations,
      and predictive analytics for the Romanian marketplace ecosystem.
    goal: |
      Provide predictive market analysis, trend forecasting, and strategic insights
      through advanced pattern recognition and data analysis.
    tasks:
      trend_analysis_task:
        description: |
          Analyze historical data, identify patterns, predict future trends,
          and provide strategic recommendations for marketplace optimization.
        expected_output: |
          Comprehensive trend analysis with predictions and strategic recommendations.

  demand_predictor:
    role: Demand Prediction and Optimization Expert
    backstory: |
      You excel at predicting demand patterns, identifying high-opportunity categories,
      and optimizing marketplace strategies based on demand forecasting.
    goal: |
      Provide accurate demand predictions, identify growth opportunities,
      and optimize marketplace positioning through predictive analytics.
    tasks:
      demand_prediction_task:
        description: |
          Predict demand patterns, identify high-growth categories, analyze seasonal variations,
          and provide optimization recommendations for marketplace strategy.
        expected_output: |
          Demand forecasting report with growth opportunities and optimization strategies.

  competitive_analyzer:
    role: Competitive Intelligence Specialist
    backstory: |
      You analyze competitive positioning, pricing strategies, and market dynamics
      to provide strategic insights for marketplace competitiveness.
    goal: |
      Provide competitive intelligence, benchmark analysis, and strategic positioning
      recommendations for marketplace optimization.
    tasks:
      competitive_analysis_task:
        description: |
          Analyze competitive landscape, benchmark pricing strategies, identify market gaps,
          and provide strategic positioning recommendations.
        expected_output: |
          Competitive intelligence report with benchmarking and strategic recommendations.

# Autonomous workflow configuration
autonomous_workflows:
  continuous_monitoring:
    frequency: "realtime"
    adaptation_triggers: ["price_changes", "demand_spikes", "new_listings", "category_shifts"]
    feedback_loops: true
    learning_enabled: true

  predictive_analytics:
    schedule: "daily"
    prediction_horizon: "30_days"
    confidence_threshold: 0.85
    auto_adaptation: true

# Environment feedback configuration
feedback_mechanisms:
  market_response_tracking: true
  user_behavior_analysis: true
  performance_optimization: true
  strategy_adaptation: true
