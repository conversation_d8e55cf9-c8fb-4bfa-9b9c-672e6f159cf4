name: Marketing Automation Agent
description: Advanced marketing automation agent for TikTok, Instagram, Facebook, and Google Ads with n8n and Flowise integration
version: 2.0.0

# Future-ready architecture for marketplace scaling
architecture:
  type: distributed
  platforms:
    - n8n
    - Flowise
    - Azure MCP
    - VSCode Extension
  channels:
    - tiktok
    - instagram
    - facebook
    - google_ads
    - youtube
    - linkedin

tools:
  # Content Generation Tools
  - name: generate_ad_copy
    description: Generate compelling ad copy for different platforms
    endpoint: /ai-assistant/marketing/generate-ad/
    method: POST
    platforms: [tiktok, instagram, facebook, google_ads]
    parameters:
      - name: platform
        type: string
        required: true
        enum: [tiktok, instagram, facebook, google_ads, youtube, linkedin]
      - name: product_info
        type: object
        required: true
        description: Product details including features, benefits, target audience
      - name: campaign_type
        type: string
        required: true
        enum: [awareness, consideration, conversion, retention]
      - name: tone
        type: string
        default: "professional"
        enum: [professional, casual, humorous, urgent, luxury]

  - name: create_visual_content
    description: Generate visual content briefs and specifications
    endpoint: /ai-assistant/marketing/visual-brief/
    method: POST
    parameters:
      - name: platform
        type: string
        required: true
      - name: content_type
        type: string
        required: true
        enum: [image, video, carousel, story, reel]
      - name: brand_guidelines
        type: object
        description: Brand colors, fonts, style guidelines

  # Campaign Management Tools
  - name: optimize_campaign
    description: Optimize running campaigns based on performance data
    endpoint: /ai-assistant/marketing/optimize/
    method: POST
    parameters:
      - name: campaign_data
        type: object
        required: true
        description: Current campaign metrics and performance data
      - name: optimization_goal
        type: string
        required: true
        enum: [ctr, cpc, cpm, roas, conversion_rate]

  - name: audience_analysis
    description: Analyze and segment audiences for better targeting
    endpoint: /ai-assistant/marketing/audience/
    method: POST
    parameters:
      - name: customer_data
        type: object
        required: true
        description: Customer demographics, behavior, purchase history
      - name: platform
        type: string
        required: true

  # Integration Tools
  - name: sync_n8n_workflow
    description: Sync with n8n automation workflows
    endpoint: /ai-assistant/integrations/n8n/
    method: POST
    parameters:
      - name: workflow_config
        type: object
        required: true
        description: n8n workflow configuration
      - name: trigger_type
        type: string
        required: true
        enum: [webhook, schedule, event, manual]

  - name: sync_flowise_agent
    description: Sync with Flowise AI agents
    endpoint: /ai-assistant/integrations/flowise/
    method: POST
    parameters:
      - name: agent_config
        type: object
        required: true
        description: Flowise agent configuration
      - name: flow_type
        type: string
        required: true
        enum: [chatflow, workflow, autonomous]

# Azure MCP Integration
azure_mcp:
  enabled: true
  services:
    - cognitive_services
    - openai
    - computer_vision
    - text_analytics
  deployment:
    region: eastus
    scale: auto
    monitoring: enabled

# VSCode Extension Integration
vscode_extension:
  enabled: true
  features:
    - inline_code_refactoring
    - real_time_suggestions
    - marketplace_integration
    - git_hooks
  commands:
    - "Refact: Analyze Code"
    - "Refact: Get Suggestions"
    - "Refact: Apply Changes"
    - "Refact: Optimize Imports"

# n8n Integration Templates
n8n_templates:
  tiktok_ads:
    triggers:
      - new_listing_created
      - price_updated
      - inventory_low
    actions:
      - generate_video_script
      - create_hashtag_set
      - schedule_posts
      - monitor_engagement

  instagram_automation:
    triggers:
      - new_product_launch
      - seasonal_campaign
      - user_generated_content
    actions:
      - create_story_templates
      - generate_captions
      - schedule_feed_posts
      - auto_respond_comments

  facebook_ads:
    triggers:
      - campaign_performance_drop
      - competitor_analysis
      - market_trend_detected
    actions:
      - adjust_targeting
      - update_creative
      - optimize_budget
      - A/B_test_setup

  google_ads:
    triggers:
      - search_trend_spike
      - competitor_bid_change
      - conversion_rate_drop
    actions:
      - keyword_expansion
      - ad_copy_refresh
      - bid_adjustment
      - negative_keyword_add

# Flowise Agent Configurations
flowise_agents:
  content_creator:
    type: autonomous
    capabilities:
      - generate_daily_content
      - respond_to_comments
      - trend_analysis
      - competitor_monitoring
    
  performance_optimizer:
    type: workflow
    capabilities:
      - campaign_analysis
      - budget_optimization
      - audience_refinement
      - creative_testing
    
  customer_service:
    type: chatflow
    capabilities:
      - automated_responses
      - lead_qualification
      - complaint_handling
      - upselling

# Future Scaling Configuration
scaling:
  auto_scale:
    enabled: true
    triggers:
      - traffic_spike
      - campaign_launch
      - viral_content
  load_balancing:
    enabled: true
    strategy: round_robin
  caching:
    enabled: true
    ttl: 3600
    strategy: redis

# Monitoring and Analytics
monitoring:
  metrics:
    - engagement_rate
    - conversion_rate
    - cost_per_acquisition
    - return_on_ad_spend
    - customer_lifetime_value
  alerts:
    - performance_drop_threshold: 20
    - budget_exceeded_threshold: 80
    - negative_feedback_threshold: 5
  reporting:
    frequency: daily
    format: json
    destinations:
      - email
      - slack
      - dashboard

# Configuration for different environments
environments:
  development:
    debug: true
    mock_data: true
    rate_limit: 1000/hour
  staging:
    debug: false
    mock_data: false
    rate_limit: 5000/hour
  production:
    debug: false
    mock_data: false
    rate_limit: 50000/hour
    caching: true
    cdn: true

# API Configuration
api:
  base_url: "${API_BASE_URL:-http://localhost:8000}"
  auth:
    type: jwt
    refresh_token: true
    session_timeout: 3600
  rate_limiting:
    enabled: true
    strategy: sliding_window
    limits:
      requests_per_minute: 100
      requests_per_hour: 1000
      requests_per_day: 10000

# Integration Endpoints
endpoints:
  n8n_webhook: "${N8N_WEBHOOK_URL}"
  flowise_api: "${FLOWISE_API_URL}"
  azure_mcp: "${AZURE_MCP_ENDPOINT}"
  vscode_extension: "vscode://piata-ro.refact-agent"
