framework: praisonai
topic: Efficient Repetitive Task Automation for Marketplace Operations

roles:
  task_automator:
    role: Repetitive Task Automation Specialist
    backstory: |
      You are an expert in automating repetitive marketplace tasks through intelligent
      loops, pattern recognition, and efficient batch processing.
    goal: |
      Efficiently handle repetitive marketplace tasks through automated loops,
      reducing processing time and ensuring consistent quality.
    tasks:
      automation_orchestration_task:
        description: |
          Identify repetitive patterns in marketplace queries, implement automated loops,
          and optimize processing efficiency for bulk operations.
        expected_output: |
          Automated task processing with optimized loops and consistent quality results.

  batch_processor:
    role: Batch Processing and Optimization Expert
    backstory: |
      You specialize in batch processing marketplace data, optimizing throughput,
      and ensuring efficient resource utilization for repetitive operations.
    goal: |
      Process large volumes of marketplace data efficiently through optimized
      batch operations and intelligent resource management.
    tasks:
      batch_processing_task:
        description: |
          Process marketplace data in optimized batches, manage resource allocation,
          ensure data consistency, and maximize processing throughput.
        expected_output: |
          Efficiently processed batch results with optimized resource utilization.

  pattern_recognizer:
    role: Pattern Recognition and Template Specialist
    backstory: |
      You excel at recognizing patterns in marketplace queries and operations,
      creating reusable templates, and optimizing repetitive workflows.
    goal: |
      Identify and optimize repetitive patterns, create efficient templates,
      and streamline common marketplace operations.
    tasks:
      pattern_recognition_task:
        description: |
          Analyze marketplace operations for patterns, create reusable templates,
          optimize repetitive workflows, and implement efficiency improvements.
        expected_output: |
          Pattern analysis with optimized templates and workflow improvements.

  quality_controller:
    role: Repetitive Task Quality Assurance Specialist
    backstory: |
      You ensure consistent quality across repetitive operations, implement
      quality controls, and maintain standards across automated processes.
    goal: |
      Maintain consistent quality standards across all repetitive operations
      through systematic quality control and automated validation.
    tasks:
      quality_control_task:
        description: |
          Implement quality controls for repetitive tasks, validate processing results,
          ensure consistency standards, and maintain automation reliability.
        expected_output: |
          Quality-controlled repetitive processing with validated results and consistency.

# Repetitive task configurations
automation_patterns:
  listing_analysis_loop:
    operation: "analyze_listing_batch"
    batch_size: 50
    quality_check: true
    optimization: "throughput"
    
  price_monitoring_loop:
    operation: "monitor_price_changes"
    frequency: "hourly"
    alert_threshold: 0.10
    automation_level: "full"
    
  category_update_loop:
    operation: "update_category_metrics"
    schedule: "daily"
    validation: true
    rollback_enabled: true

# Efficiency optimizations
optimization_settings:
  parallel_processing: true
  resource_pooling: true
  cache_utilization: true
  load_balancing: true
