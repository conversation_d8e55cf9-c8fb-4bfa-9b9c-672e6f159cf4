name: Refact Agent
description: AI-powered code refactoring agent that interfaces with the Piața.ro AI Assistant API
version: 1.0.0

tools:
  - name: analyze_code
    description: Analyze code for refactoring opportunities using AI
    endpoint: /ai-assistant/chat/
    method: POST
    auth:
      type: session
      required: true
    parameters:
      - name: message
        type: string
        required: true
        description: Code analysis request in natural language
      - name: conversation_id
        type: string
        required: false
        description: Existing conversation ID for context

  - name: get_refactoring_suggestions
    description: Get specific refactoring suggestions for code
    endpoint: /ai-assistant/chat/
    method: POST  
    auth:
      type: session
      required: true
    parameters:
      - name: message
        type: string
        required: true
        description: Code to refactor with specific instructions
      - name: conversation_id
        type: string
        required: false

  - name: apply_refactoring
    description: Apply suggested refactoring changes
    endpoint: /ai-assistant/chat/
    method: POST
    auth:
      type: session
      required: true
    parameters:
      - name: message
        type: string
        required: true
        description: Confirmation to apply changes
      - name: changes
        type: object
        required: true
        description: The changes to apply

configuration:
  base_url: http://localhost:8000/admin
  auth:
    username: ${ADMIN_USER}
    password: ${ADMIN_PASS}
  timeout: 30
  max_retries: 3

prompts:
  code_analysis: |
    Analyze the following code for refactoring opportunities:
    {code}
    
    Consider:
    - Performance improvements
    - Readability enhancements  
    - Modern syntax alternatives
    - Bug detection
    - Security vulnerabilities

  get_suggestions: |
    Provide specific refactoring suggestions for:
    {code}
    
    Focus on:
    - Concrete change recommendations
    - Before/after examples
    - Impact analysis

  apply_changes: |
    Apply these refactoring changes:
    {changes}
    
    To this code:
    {original_code}
    
    Return the fully refactored code.
