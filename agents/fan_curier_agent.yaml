name: Fan-Curier Agent
description: AI-powered courier integration agent for Fan-Curier shipping services
version: 1.0.0

tools:
  - name: create_shipment
    description: Create a new shipment with Fan-Curier
    endpoint: /mcp/fan-curier/create-shipment/
    method: POST
    auth:
      type: api_key
      required: true
    parameters:
      - name: sender_data
        type: object
        required: true
        description: Sender information (name, address, phone, email)
      - name: recipient_data
        type: object
        required: true
        description: Recipient information (name, address, phone, email)
      - name: package_data
        type: object
        required: true
        description: Package details (weight, dimensions, value, contents)
      - name: service_type
        type: string
        required: false
        default: "standard"
        description: Delivery service type (standard, express, same_day)

  - name: track_shipment
    description: Track shipment status and location
    endpoint: /mcp/fan-curier/track/
    method: GET
    auth:
      type: api_key
      required: true
    parameters:
      - name: awb_number
        type: string
        required: true
        description: AWB (Air Waybill) tracking number
      - name: detailed
        type: boolean
        required: false
        default: false
        description: Get detailed tracking information

  - name: calculate_shipping_cost
    description: Calculate shipping cost for a package
    endpoint: /mcp/fan-curier/calculate-cost/
    method: POST
    auth:
      type: api_key
      required: true
    parameters:
      - name: origin_locality
        type: string
        required: true
        description: Origin city/locality
      - name: destination_locality
        type: string
        required: true
        description: Destination city/locality
      - name: weight
        type: number
        required: true
        description: Package weight in kg
      - name: declared_value
        type: number
        required: false
        description: Declared value for insurance
      - name: service_type
        type: string
        required: false
        default: "standard"

  - name: get_localities
    description: Get list of available localities for shipping
    endpoint: /mcp/fan-curier/localities/
    method: GET
    auth:
      type: api_key
      required: true
    parameters:
      - name: county
        type: string
        required: false
        description: Filter by county (județ)
      - name: search
        type: string
        required: false
        description: Search localities by name

  - name: schedule_pickup
    description: Schedule package pickup from seller
    endpoint: /mcp/fan-curier/schedule-pickup/
    method: POST
    auth:
      type: api_key
      required: true
    parameters:
      - name: pickup_address
        type: object
        required: true
        description: Pickup address details
      - name: contact_person
        type: object
        required: true
        description: Contact person information
      - name: pickup_date
        type: string
        required: true
        description: Preferred pickup date (YYYY-MM-DD)
      - name: time_interval
        type: string
        required: false
        default: "09:00-17:00"
        description: Pickup time interval

  - name: cancel_shipment
    description: Cancel an existing shipment
    endpoint: /mcp/fan-curier/cancel/
    method: POST
    auth:
      type: api_key
      required: true
    parameters:
      - name: awb_number
        type: string
        required: true
        description: AWB number to cancel

configuration:
  base_url: https://api.fancourier.ro/
  api_version: v1
  auth:
    api_key: ${FAN_CURIER_API_KEY}
    client_id: ${FAN_CURIER_CLIENT_ID}
  timeout: 30
  max_retries: 3
  rate_limit:
    requests_per_minute: 60
    burst_limit: 10

prompts:
  create_shipment: |
    Create a new shipment for marketplace transaction:
    
    Listing: {listing_title}
    Seller: {seller_name} - {seller_phone}
    Buyer: {buyer_name} - {buyer_phone}
    
    Package Details:
    - Weight: {weight}kg
    - Dimensions: {dimensions}
    - Value: {value} RON
    - Contents: {contents}
    
    Delivery Address: {delivery_address}
    
    Please create the shipment and provide AWB number for tracking.

  track_package: |
    Track shipment with AWB: {awb_number}
    
    Provide current status, location, and estimated delivery time.
    If there are any issues, suggest next steps.

  calculate_cost: |
    Calculate shipping cost for:
    - From: {origin}
    - To: {destination}
    - Weight: {weight}kg
    - Value: {value} RON
    - Service: {service_type}
    
    Provide cost breakdown and delivery timeframe.

integration:
  marketplace_events:
    - order_confirmed: auto_create_shipment
    - payment_released: schedule_pickup
    - shipment_delivered: update_order_status
  
  notifications:
    - tracking_updates: notify_buyer_seller
    - delivery_confirmation: release_escrow_funds
    - delivery_issues: escalate_to_support

localities:
  # Major Romanian cities and counties for shipping
  major_cities:
    - București
    - Cluj-Napoca
    - Timișoara
    - Iași
    - Constanța
    - Craiova
    - Brașov
    - Galați
    - Ploiești
    - Oradea
  
  counties:
    - Alba
    - Arad
    - Argeș
    - Bacău
    - Bihor
    - Bistrița-Năsăud
    - Botoșani
    - Brașov
    - Brăila
    - București
    - Buzău
    - Caraș-Severin
    - Călărași
    - Cluj
    - Constanța
    - Covasna
    - Dâmbovița
    - Dolj
    - Galați
    - Giurgiu
    - Gorj
    - Harghita
    - Hunedoara
    - Ialomița
    - Iași
    - Ilfov
    - Maramureș
    - Mehedinți
    - Mureș
    - Neamț
    - Olt
    - Prahova
    - Satu Mare
    - Sălaj
    - Sibiu
    - Suceava
    - Teleorman
    - Timiș
    - Tulcea
    - Vaslui
    - Vâlcea
    - Vrancea
