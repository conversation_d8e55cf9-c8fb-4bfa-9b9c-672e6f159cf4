# Piața.ro Developer Instructions

This document provides instructions for developers working on the Piața.ro project.

## 1. Project Overview

Piața.ro is a modern online marketplace for Romania, built with Django. It features a robust set of functionalities including user authentication, ad listings, image uploads, and an advanced AI Assistant to help with various tasks. The project is containerized using Docker for consistent development and deployment environments.

## 2. Getting Started (Local Development)

The project is designed to be run with Docker and Docker Compose.

### Prerequisites

*   Git
*   Docker
*   Docker Compose

### Setup Steps

1.  **Clone the repository:**
    ```bash
    git clone <repository_url>
    cd piata-ro-project
    ```

2.  **Create your environment file:**
    Copy the example environment file to create your own local configuration.
    ```bash
    cp .env.example .env
    ```
    Review the `.env` file and fill in any necessary values, especially for external services if you plan to test them (e.g., `GOOGLE_MAPS_API_KEY`, `DEEPSEEK_API_KEY`). For basic local development, the defaults should be sufficient.

3.  **Build and start the services:**
    The `deploy.sh` script handles the initial setup.
    ```bash
    ./deploy.sh
    ```
    This command will build the Docker images, start the containers (web, db, etc.), and apply database migrations.

4.  **Create a superuser:**
    To access the Django admin, you'll need a superuser account.
    ```bash
    docker-compose exec web python manage.py createsuperuser
    ```
    Follow the prompts to create your admin user.

5.  **Access the application:**
    *   **Website:** [http://localhost:8000](http://localhost:8000)
    *   **Django Admin:** [http://localhost:8000/admin](http://localhost:8000/admin)

## 3. Project Structure

Here is an overview of the most important directories:

*   `/piata_ro/`: Main Django project configuration, settings (`settings.py`), and URL routing (`urls.py`).
*   `/marketplace/`: The core Django app containing models, views, and templates for the marketplace features.
*   `/ai_assistant/`: Contains the code for the AI assistant, including MCP servers and integrations.
*   `/agents/`: Configuration files for various AI agents.
*   `/static/`: Static files (CSS, JavaScript, images).
*   `/templates/`: Django HTML templates.
*   `/tests/`: Project-wide integration and unit tests.
*   `docker-compose.yml`: Defines the services, networks, and volumes for the Dockerized application.
*   `manage.py`: The Django management script.

## 4. Running the Application

*   **To start all services in detached mode:**
    ```bash
    docker-compose up -d
    ```
*   **To stop all services:**
    ```bash
    docker-compose down
    ```
*   **To view logs:**
    ```bash
    # View logs for all services
    docker-compose logs -f

    # View logs for a specific service (e.g., the web server)
    docker-compose logs -f web
    ```

*   **Starting AI Agents:**
    The project includes several AI agents that run as separate processes. Use the provided shell scripts to start them in separate terminals as needed.
    ```bash
    # Example for the refactoring agent
    ./start_refact_agent.sh
    ```

## 5. Running Tests

To run the entire test suite, execute the following command:

```bash
docker-compose exec web python manage.py test
```

To run tests for a specific app:

```bash
docker-compose exec web python manage.py test marketplace
```

## 6. Key Features

*   **Authentication**: Handled by `django-allauth`. Configuration is in `piata_ro/settings.py`. Templates are in `templates/account/`.
*   **Google OAuth**: Setup instructions are in `GOOGLE_OAUTH_SETUP.md`.
*   **Listings**: The core models are in `marketplace/models.py` (`Listing`, `Category`). Views are in `marketplace/views/`.
*   **AI Assistant**: The main logic is in `ai_assistant/`. See `AI_ASSISTANT_SETUP.md` for detailed information on its architecture and usage.
*   **OpenStreetMap Integration**: Used for location services. See `PULL_REQUEST_DESCRIPTION.md` for details on the implementation.

## 7. AI-Assisted Development

The AI assistant is a key part of the development workflow. It can be used for tasks like code analysis, refactoring, and more.

*   **Accessing the Assistant**: The primary interface is through the Django admin.
*   **VSCode Integration**: The `ai_assistant/vscode_integration.py` script provides command-line tools for interacting with the assistant from your editor.
    ```bash
    # Example: Analyze a file
    python ai_assistant/vscode_integration.py analyze path/to/your/file.py
    ```
    Refer to `AI_ASSISTANT_SETUP.md` for more commands and details.

## 8. Deployment

The application is designed for deployment to Azure.

*   **Deployment Guide**: The `README-PRODUCTION.md` file contains a detailed guide for deploying the application to Azure using Docker and Azure CLI.
*   **Production Configuration**: Production-specific settings are in `settings_prod.py`. The `production.env` file should be used for environment variables in production.
*   **Checklist**: Before deploying to production, review the `PRODUCTION_READINESS.md` checklist to ensure all security, performance, and configuration tasks are completed.

Happy coding!
