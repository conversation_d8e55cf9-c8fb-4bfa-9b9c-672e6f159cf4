#!/bin/bash

# Fan-Curier MCP Agent Startup Script
# Provides shipping and logistics integration for Romanian marketplace

echo "🚚 Starting Fan-Curier MCP Agent..."

# Set environment variables
export FAN_CURIER_API_KEY="${FAN_CURIER_API_KEY:-demo_key}"
export FAN_CURIER_CLIENT_ID="${FAN_CURIER_CLIENT_ID:-demo_client}"

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
    echo "✅ Virtual environment activated"
fi

# Install required dependencies
echo "📦 Installing MCP dependencies..."
pip install -q mcp requests asyncio

# Check if the MCP server file exists
if [ ! -f "ai_assistant/mcp_fan_curier_server.py" ]; then
    echo "❌ Fan-Curier MCP server not found!"
    exit 1
fi

# Make the server executable
chmod +x ai_assistant/mcp_fan_curier_server.py

echo "🔧 Fan-Curier Agent Configuration:"
echo "   - API Endpoint: https://api.fancourier.ro/v1"
echo "   - Services: Standard, Express, Same Day, Economy"
echo "   - Coverage: All Romanian counties and localities"
echo "   - Features: Shipment creation, tracking, cost calculation"

echo ""
echo "🚀 Starting Fan-Curier MCP Server..."
echo "   Server will handle:"
echo "   ✓ Shipment creation and management"
echo "   ✓ Real-time package tracking"
echo "   ✓ Shipping cost calculations"
echo "   ✓ Pickup scheduling"
echo "   ✓ Delivery proof and confirmations"
echo ""

# Start the MCP server
python ai_assistant/mcp_fan_curier_server.py

echo "🛑 Fan-Curier Agent stopped"
