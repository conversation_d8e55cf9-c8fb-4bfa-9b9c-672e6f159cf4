"""
MCP Orchestrator - Wrapper for Smart MCP Orchestrator
This file provides backward compatibility and simpler import interface
"""

import httpx
from django.conf import settings
from .smart_mcp_orchestrator import SmartMCPOrchestrator
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class MCPOrchestrator:
    """
    Main MCP Orchestrator class that wraps the Smart Pydantic-based orchestrator
    Provides a simple interface for the Django views
    """
    
    def __init__(self):
        """Initialize the orchestrator with the smart Pydantic model"""
        try:
            self.smart_orchestrator = SmartMCPOrchestrator()
            logger.info("MCP Orchestrator initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize MCP Orchestrator: {e}")
            raise
    
    async def process_request(self, user_message: str, conversation_history: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        Process user request using the smart orchestrator with fallback to direct LLM
        
        Args:
            user_message: The user's message/request
            conversation_history: Optional conversation history
            
        Returns:
            Dict containing response, tools_used, and tool_results
        """
        try:
            # First try with the smart orchestrator
            try:
                mcp_response = await self.smart_orchestrator.process_request(user_message, conversation_history)
                return {
                    'response': mcp_response.response,
                    'tools_used': mcp_response.tools_used,
                    'tool_results': mcp_response.tool_results,
                    'intent_analysis': mcp_response.intent_analysis.dict() if mcp_response.intent_analysis else None,
                    'reasoning': mcp_response.reasoning
                }
            except Exception as mcp_error:
                logger.warning(f"Falling back to direct LLM due to MCP error: {mcp_error}")
                
                # Fallback to direct LLM response
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        "https://api.deepseek.com/v1/chat/completions",
                        headers={
                            'Content-Type': 'application/json',
                            'Authorization': f'Bearer {settings.DEEPSEEK_API_KEY}'
                        },
                        json={
                            'model': 'deepseek-chat',
                            'messages': [
                                {
                                    'role': 'system',
                                    'content': 'You are an AI assistant for Piața.ro marketplace admin panel.'
                                },
                                {
                                    'role': 'user',
                                    'content': user_message
                                }
                            ],
                            'max_tokens': 500,
                            'temperature': 0.3
                        },
                        timeout=30.0
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        llm_response = result['choices'][0]['message']['content']
                        return {
                            'response': llm_response,
                            'tools_used': [],
                            'tool_results': [],
                            'intent_analysis': None,
                            'reasoning': 'Used direct LLM fallback'
                        }
                    elif response.status_code == 401:
                        logger.error("Invalid DeepSeek API key")
                        return {
                            'response': "I'm having trouble connecting to the AI service. Please check the API configuration.",
                            'tools_used': [],
                            'tool_results': [],
                            'intent_analysis': None,
                            'reasoning': 'Invalid API key'
                        }
                    elif response.status_code == 429:
                        logger.error("Rate limited by DeepSeek API")
                        return {
                            'response': "I'm currently receiving too many requests. Please try again shortly.",
                            'tools_used': [],
                            'tool_results': [],
                            'intent_analysis': None,
                            'reasoning': 'Rate limited'
                        }
                    else:
                        logger.error(f"DeepSeek API error: {response.status_code} - {response.text}")
                        return {
                            'response': "I'm experiencing technical difficulties. Please try again later.",
                            'tools_used': [],
                            'tool_results': [],
                            'intent_analysis': None,
                            'reasoning': f"API error: {response.status_code}"
                        }
            
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            return {
                'response': f"I apologize, but I encountered an error while processing your request: {str(e)}",
                'tools_used': [],
                'tool_results': [],
                'intent_analysis': None,
                'reasoning': f"Error occurred: {str(e)}"
            }
    
    def get_server_status(self) -> Dict[str, Any]:
        """Get status of all MCP servers"""
        try:
            servers = self.smart_orchestrator.mcp_servers
            status = {}
            
            for server_type, config in servers.items():
                status[server_type.value] = {
                    'name': config.name,
                    'url': config.url,
                    'port': config.port,
                    'description': config.description,
                    'tools': config.tools,
                    'status': 'simulated'  # Until real SSE implementation
                }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting server status: {e}")
            return {}
