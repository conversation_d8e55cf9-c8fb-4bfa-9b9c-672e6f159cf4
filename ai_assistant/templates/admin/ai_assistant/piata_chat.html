{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<style>
    .chat-container {
        max-width: 1200px;
        margin: 20px auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .chat-header {
        background: linear-gradient(135deg, #0056b3 0%, #004494 100%);
        color: white;
        padding: 15px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .chat-header h2 {
        margin: 0;
        font-size: 1.5rem;
    }
    
    .chat-body {
        display: flex;
        height: calc(100vh - 250px);
        min-height: 500px;
    }
    
    .chat-sidebar {
        width: 250px;
        background: #f5f7fa;
        border-right: 1px solid #e9ecef;
        overflow-y: auto;
        padding: 15px;
    }
    
    .chat-main {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .chat-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #f9f9f9;
    }
    
    .message {
        margin-bottom: 15px;
        max-width: 80%;
    }
    
    .message-user {
        margin-left: auto;
        background: #0056b3;
        color: white;
        border-radius: 18px 18px 0 18px;
        padding: 10px 15px;
    }
    
    .message-assistant {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 18px 18px 18px 0;
        padding: 10px 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }
    
    .chat-input {
        padding: 15px;
        border-top: 1px solid #e9ecef;
        background: white;
    }
    
    .chat-form {
        display: flex;
        gap: 10px;
    }
    
    .chat-textarea {
        flex: 1;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 10px;
        resize: none;
        min-height: 60px;
        font-family: inherit;
    }
    
    .chat-textarea:focus {
        outline: none;
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .chat-submit {
        background: #0056b3;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 0 20px;
        cursor: pointer;
        font-weight: 600;
        transition: background 0.2s;
    }
    
    .chat-submit:hover {
        background: #004494;
    }
    
    .sidebar-title {
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .sidebar-section {
        margin-bottom: 20px;
    }
    
    .sidebar-item {
        padding: 8px 10px;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.2s;
        margin-bottom: 5px;
        font-size: 0.9rem;
    }
    
    .sidebar-item:hover {
        background: #e9ecef;
    }
    
    .sidebar-item.active {
        background: #e2e8f0;
        font-weight: 500;
    }
    
    .result-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
        font-size: 0.9rem;
    }
    
    .result-table th, .result-table td {
        border: 1px solid #e9ecef;
        padding: 8px 12px;
        text-align: left;
    }
    
    .result-table th {
        background: #f8f9fa;
        font-weight: 600;
    }
    
    .result-table tr:nth-child(even) {
        background: #f9f9f9;
    }
    
    .error-message {
        color: #dc3545;
        padding: 10px;
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 4px;
    }
    
    .success-message {
        color: #28a745;
        padding: 10px;
        background: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 4px;
    }
    
    .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(0, 86, 179, 0.3);
        border-radius: 50%;
        border-top-color: #0056b3;
        animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="chat-container">
    <div class="chat-header">
        <h2>
            <i class="fas fa-robot" style="margin-right: 10px;"></i>
            Piața.ro AI Assistant
        </h2>
        <div>
            <span id="status-indicator" style="font-size: 0.8rem; opacity: 0.8;">Ready</span>
        </div>
    </div>
    
    <div class="chat-body">
        <div class="chat-sidebar">
            <div class="sidebar-section">
                <div class="sidebar-title">Sample Queries</div>
                <div class="sidebar-item" onclick="insertQuery('SELECT * FROM marketplace_category LIMIT 10')">
                    Show categories
                </div>
                <div class="sidebar-item" onclick="insertQuery('SELECT * FROM marketplace_listing LIMIT 10')">
                    Show listings
                </div>
                <div class="sidebar-item" onclick="insertQuery('SELECT COUNT(*) FROM marketplace_listing')">
                    Count listings
                </div>
                <div class="sidebar-item" onclick="insertQuery('SELECT COUNT(*) FROM auth_user')">
                    Count users
                </div>
            </div>
            
            <div class="sidebar-section">
                <div class="sidebar-title">Recent Queries</div>
                <div id="recent-queries">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
        
        <div class="chat-main">
            <div class="chat-messages" id="chat-messages">
                <div class="message message-assistant">
                    <p>Welcome to Piața.ro AI Assistant! I can help you query the database and analyze data.</p>
                    <p>Try asking me to show categories, listings, or run SQL queries.</p>
                </div>
            </div>
            
            <div class="chat-input">
                <form id="query-form" class="chat-form">
                    <textarea 
                        id="query-input" 
                        name="query" 
                        class="chat-textarea" 
                        placeholder="Enter your SQL query or ask a question..."
                        rows="3"
                    ></textarea>
                    <button type="submit" class="chat-submit">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Store recent queries
    let recentQueries = [];
    
    // Function to add a message to the chat
    function addMessage(content, isUser = false) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'message-user' : 'message-assistant'}`;
        messageDiv.innerHTML = content;
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Function to format SQL results as HTML table
    function formatSQLResults(data) {
        if (data.error) {
            return `<div class="error-message">${data.error}</div>`;
        } else if (data.message) {
            return `<div class="success-message">${data.message}</div>`;
        } else if (data.rows) {
            let html = '<table class="result-table"><thead><tr>';
            data.columns.forEach(col => html += `<th>${col}</th>`);
            html += '</tr></thead><tbody>';
            
            if (data.rows.length === 0) {
                html += '<tr><td colspan="' + data.columns.length + '">No results found</td></tr>';
            } else {
                data.rows.forEach(row => {
                    html += '<tr>';
                    row.forEach(cell => html += `<td>${cell !== null ? cell : '<em>NULL</em>'}</td>`);
                    html += '</tr>';
                });
            }
            
            html += '</tbody></table>';
            return html;
        }
        return '<div>No results</div>';
    }
    
    // Function to insert a query into the input field
    function insertQuery(query) {
        document.getElementById('query-input').value = query;
        document.getElementById('query-input').focus();
    }
    
    // Function to update recent queries sidebar
    function updateRecentQueries() {
        const container = document.getElementById('recent-queries');
        container.innerHTML = '';
        
        recentQueries.forEach(query => {
            const div = document.createElement('div');
            div.className = 'sidebar-item';
            div.textContent = query.length > 30 ? query.substring(0, 30) + '...' : query;
            div.onclick = () => insertQuery(query);
            container.appendChild(div);
        });
        
        if (recentQueries.length === 0) {
            const div = document.createElement('div');
            div.textContent = 'No recent queries';
            div.style.padding = '8px 10px';
            div.style.color = '#6c757d';
            div.style.fontStyle = 'italic';
            div.style.fontSize = '0.9rem';
            container.appendChild(div);
        }
    }
    
    // Initialize recent queries
    updateRecentQueries();
    
    // Handle form submission
    document.getElementById('query-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const query = document.getElementById('query-input').value.trim();
        
        if (!query) return;
        
        // Add user message
        addMessage(`<pre>${query}</pre>`, true);
        
        // Add to recent queries
        if (!recentQueries.includes(query)) {
            recentQueries.unshift(query);
            if (recentQueries.length > 5) {
                recentQueries.pop();
            }
            updateRecentQueries();
        }
        
        // Clear input
        document.getElementById('query-input').value = '';
        
        // Update status
        document.getElementById('status-indicator').innerHTML = '<div class="loading"></div> Processing...';
        
        // Send request
        fetch(window.location.pathname, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({ query: query })
        })
        .then(response => response.json())
        .then(data => {
            // Add response message
            addMessage(formatSQLResults(data));
            document.getElementById('status-indicator').textContent = 'Ready';
        })
        .catch(error => {
            addMessage(`<div class="error-message">Error: ${error.message}</div>`);
            document.getElementById('status-indicator').textContent = 'Error';
        });
    });
    
    // Auto-resize textarea
    const textarea = document.getElementById('query-input');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Focus input on load
    window.addEventListener('load', function() {
        document.getElementById('query-input').focus();
    });
</script>
{% endblock %}