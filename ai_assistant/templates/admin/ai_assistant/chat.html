{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Piața.ro AI Assistant{% endblock %}

{% block extrahead %}
<style>
    .ai-assistant-container {
        max-width: 1200px;
        margin: 20px auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .ai-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px 8px 0 0;
    }
    
    .ai-status {
        display: flex;
        gap: 15px;
        margin-top: 10px;
    }
    
    .status-item {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
    }
    
    .status-online {
        background: rgba(46, 204, 113, 0.2);
        color: #27ae60;
    }
    
    .status-offline {
        background: rgba(231, 76, 60, 0.2);
        color: #e74c3c;
    }
    
    .chat-container {
        height: 500px;
        display: flex;
        flex-direction: column;
    }
    
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        border-bottom: 1px solid #eee;
    }
    
    .message {
        margin-bottom: 15px;
        padding: 10px 15px;
        border-radius: 18px;
        max-width: 80%;
    }
    
    .message-user {
        background: #007bff;
        color: white;
        margin-left: auto;
        text-align: right;
    }
    
    .message-assistant {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #dee2e6;
    }
    
    .message-meta {
        font-size: 11px;
        opacity: 0.7;
        margin-top: 5px;
    }
    
    .chat-input-container {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 0 0 8px 8px;
    }
    
    .chat-input {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 25px;
        outline: none;
        font-size: 14px;
    }
    
    .chat-send {
        position: absolute;
        right: 35px;
        top: 50%;
        transform: translateY(-50%);
        background: #007bff;
        color: white;
        border: none;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        cursor: pointer;
    }
    
    .conversations-sidebar {
        width: 250px;
        background: #f8f9fa;
        border-right: 1px solid #dee2e6;
        padding: 15px;
    }
    
    .conversation-item {
        padding: 10px;
        border-radius: 5px;
        cursor: pointer;
        margin-bottom: 5px;
        border: 1px solid transparent;
    }
    
    .conversation-item:hover {
        background: white;
        border-color: #dee2e6;
    }
    
    .conversation-item.active {
        background: #007bff;
        color: white;
    }
    
    .main-chat {
        flex: 1;
    }
    
    .chat-layout {
        display: flex;
        height: 600px;
    }
</style>
{% endblock %}

{% block content %}
<div class="ai-assistant-container">
    <div class="ai-header">
        <h1>🤖 Piața.ro AI Assistant</h1>
        <p>Your intelligent marketplace management assistant</p>
        
        <div class="ai-status">
            <strong>MCP Agents Status (DeepSeek Powered):</strong>
            {% for agent, status in mcp_status.items %}
                <span class="status-item status-{{ status|lower }}">
                    {{ agent }}: {{ status }}
                </span>
            {% endfor %}
        </div>
    </div>
    
    <div class="chat-layout">
        <div class="conversations-sidebar">
            <h3>🛠️ MCP Tools</h3>
            
            <!-- Server Status Indicators -->
            <div class="server-status-section">
                <h4>Server Status</h4>
                <div id="server-status-list">
                    <div class="status-item" data-server="advertising">
                        <span class="status-dot offline"></span>
                        <span>Advertising</span>
                        <small class="response-time"></small>
                    </div>
                    <div class="status-item" data-server="database">
                        <span class="status-dot offline"></span>
                        <span>Database</span>
                        <small class="response-time"></small>
                    </div>
                    <div class="status-item" data-server="stock">
                        <span class="status-dot offline"></span>
                        <span>Stock</span>
                        <small class="response-time"></small>
                    </div>
                </div>
            </div>
            
            <!-- Tools Panel -->
            <div class="tools-section">
                <h4>Available Tools</h4>
                <div id="mcp-tools-list">
                    <!-- Tools will be loaded dynamically -->
                    <div class="loading">Loading tools...</div>
                </div>
            </div>
            
            <button id="new-conversation" class="btn btn-primary btn-sm" style="margin-top: 15px; width: 100%;">
                + New Conversation
            </button>
        </div>
        
        <div class="main-chat">
            <div class="chat-container">
                <div class="chat-messages" id="chat-messages">
                    <div class="message message-assistant">
                        <div>👋 Hello! I'm your Piața.ro AI Assistant powered by DeepSeek. I can help you with:</div>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Database:</strong> Manage listings, users, and data queries</li>
                            <li><strong>Advertising:</strong> Optimize marketing, pricing, and promotions</li>
                            <li><strong>Stock:</strong> Handle inventory, supply chain, and forecasting</li>
                        </ul>
                        <div>What would you like to help you with today?</div>
                    </div>
                </div>
                
                <div class="chat-input-container">
                    <div style="position: relative;">
                        <input type="text" id="chat-input" class="chat-input" 
                               placeholder="Ask me anything about your marketplace..." 
                               onkeypress="handleKeyPress(event)">
                        <button id="send-button" class="chat-send" onclick="sendMessage()">➤</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentConversationId = null;

// Load MCP tools and server status
async function loadMCPTools() {
    try {
        const response = await fetch('/ai/api/tools/');
        const data = await response.json();
        
        const toolsList = document.getElementById('mcp-tools-list');
        toolsList.innerHTML = '';
        
        Object.entries(data.tools).forEach(([serverName, tools]) => {
            const serverSection = document.createElement('div');
            serverSection.innerHTML = `<h5 style="margin-top: 15px; color: #6c757d; font-size: 12px;">${serverName}</h5>`;
            toolsList.appendChild(serverSection);
            
            tools.forEach(tool => {
                const toolItem = document.createElement('div');
                toolItem.className = 'tool-item';
                toolItem.innerHTML = `
                    <strong>${tool.icon} ${tool.name}</strong>
                    <small>${tool.description}</small>
                `;
                toolItem.onclick = () => insertToolCall(serverName, tool.name);
                toolsList.appendChild(toolItem);
            });
        });
    } catch (error) {
        console.error('Error loading MCP tools:', error);
        document.getElementById('mcp-tools-list').innerHTML = '<div style="color: #dc3545;">Error loading tools</div>';
    }
}

async function updateServerStatus() {
    try {
        const serverItems = document.querySelectorAll('.status-item');
        serverItems.forEach(item => {
            const dot = item.querySelector('.status-dot');
            const responseTime = item.querySelector('.response-time');
            const server = item.dataset.server;
            
            // Simulate real-time status - in production, call actual health check API
            const statuses = ['online', 'offline'];
            const status = Math.random() > 0.2 ? 'online' : 'offline'; // 80% uptime simulation
            
            dot.className = `status-dot ${status}`;
            responseTime.textContent = status === 'online' ? `${Math.floor(Math.random() * 100 + 50)}ms` : '';
        });
    } catch (error) {
        console.error('Error updating server status:', error);
    }
}

function insertToolCall(server, tool) {
    const input = document.getElementById('chat-input');
    input.value = `/${server}.${tool}`;
    input.focus();
}

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

function sendMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessage(message, true);
    input.value = '';
    
    // Show loading
    const loadingDiv = addMessage('🤔 Thinking...', false, true);
    
    // Send to backend
    fetch('{% url "ai_assistant:ai_chat_api" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            message: message,
            conversation_id: currentConversationId
        })
    })
    .then(response => response.json())
    .then(data => {
        // Remove loading message
        loadingDiv.remove();
        
        // Add assistant response
        addMessage(data.response, false, false, data.agent_used);
        currentConversationId = data.conversation_id;
        
        // Update conversations list if new
        if (!currentConversationId) {
            location.reload(); // Simple refresh for now
        }
    })
    .catch(error => {
        loadingDiv.remove();
        addMessage('Sorry, I encountered an error. Please try again.', false);
        console.error('Error:', error);
    });
}

function addMessage(text, isUser, isLoading = false, agent = null) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${isUser ? 'user' : 'assistant'}`;
    
    let content = `<div>${text}</div>`;
    if (!isUser && agent && !isLoading) {
        content += `<div class="message-meta">via ${agent}</div>`;
    }
    
    messageDiv.innerHTML = content;
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    return messageDiv;
}

function newConversation() {
    currentConversationId = null;
    document.getElementById('chat-messages').innerHTML = `
        <div class="message message-assistant">
            <div>👋 Hello! I'm your Piața.ro AI Assistant. What can I help you with?</div>
        </div>
    `;
    
    // Remove active class from all conversations
    document.querySelectorAll('.conversation-item').forEach(item => {
        item.classList.remove('active');
    });
}

document.getElementById('new-conversation').addEventListener('click', newConversation);

// Handle conversation selection
document.querySelectorAll('.conversation-item').forEach(item => {
    item.addEventListener('click', function() {
        // Mark as active
        document.querySelectorAll('.conversation-item').forEach(i => i.classList.remove('active'));
        this.classList.add('active');
        
        currentConversationId = this.dataset.id;
        // You could load conversation history here
    });
});
</script>
{% endblock %}
