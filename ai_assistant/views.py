import json
import asyncio
import logging
from django.shortcuts import render, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.contrib import admin
from django.urls import path
from .models import Conversation, Message
from .mcp_orchestrator import MCPOrchestrator

logger = logging.getLogger(__name__)

@staff_member_required
def ai_assistant_view(request):
    """Main AI Assistant interface in Django Admin"""
    conversations = Conversation.objects.filter(user=request.user).order_by('-updated_at')[:10]
    
    # Get active conversation
    conversation_id = request.GET.get('conversation')
    active_conversation = None
    messages = []
    
    if conversation_id:
        try:
            active_conversation = Conversation.objects.get(id=conversation_id, user=request.user)
            messages = Message.objects.filter(conversation=active_conversation)
        except Conversation.DoesNotExist:
            pass
    
    # Check real MCP server status with health checks
    mcp_status = {}
    try:
        orchestrator = MCPOrchestrator()
        status = orchestrator.get_server_status()
        
        # Real health checks instead of "simulated"
        import httpx
        for server_key, config in status.items():
            try:
                # Ping each MCP server
                response = httpx.get(f"{config['url']}/health", timeout=3.0)
                mcp_status[server_key] = 'Online' if response.status_code == 200 else 'Error'
            except httpx.RequestError:
                mcp_status[server_key] = 'Offline'
            except Exception:
                mcp_status[server_key] = 'Error'
                
        # Ensure we have the expected keys
        if 'advertising' not in mcp_status:
            mcp_status['advertising'] = 'Offline'
        if 'database' not in mcp_status:
            mcp_status['database'] = 'Offline'  
        if 'stock' not in mcp_status:
            mcp_status['stock'] = 'Offline'
            
    except Exception as e:
        logger.error(f"Error checking MCP status: {e}")
        mcp_status = {'advertising': 'Error', 'database': 'Error', 'stock': 'Error'}

    context = {
        'title': 'AI Assistant - Piața.ro',
        'conversations': conversations,
        'active_conversation': active_conversation,
        'messages': messages,
        'has_permission': True,
        'mcp_status': mcp_status,
    }
    
    return render(request, 'admin/ai_assistant/chat.html', context)

@csrf_exempt
async def ai_chat_api(request):
    """API endpoint for chat with AI assistant"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    # Check authentication manually since we're using csrf_exempt
    if not request.user.is_authenticated or not request.user.is_staff:
        return JsonResponse({'error': 'Authentication required'}, status=403)
    
    try:
        data = json.loads(request.body)
        message = data.get('message', '').strip()
        conversation_id = data.get('conversation_id')
        
        if not message:
            return JsonResponse({'error': 'Message is required'}, status=400)
        
        # Get or create conversation
        if conversation_id:
            try:
                conversation = Conversation.objects.get(id=conversation_id, user=request.user)
            except Conversation.DoesNotExist:
                conversation = Conversation.objects.create(
                    user=request.user,
                    title=message[:50] + '...' if len(message) > 50 else message
                )
        else:
            conversation = Conversation.objects.create(
                user=request.user,
                title=message[:50] + '...' if len(message) > 50 else message
            )
        
        # Save user message
        user_message = Message.objects.create(
            conversation=conversation,
            role='user',
            content=message
        )
        
        # Get conversation history
        history = []
        for msg in Message.objects.filter(conversation=conversation, timestamp__lt=user_message.timestamp):
            history.append({'role': msg.role, 'content': msg.content})
        
        # Process with MCP Orchestrator
        orchestrator = MCPOrchestrator()
        
        # Await the async orchestrator directly
        result = await orchestrator.process_request(message, history)
        
        # Save assistant response
        assistant_message = Message.objects.create(
            conversation=conversation,
            role='assistant',
            content=result['response'],
            mcp_tools_used=result.get('tools_used', [])
        )
        
        return JsonResponse({
            'success': True,
            'response': result['response'],
            'conversation_id': getattr(conversation, 'id', getattr(conversation, 'pk', None)),
            'message_id': assistant_message.pk,
            'tools_used': result.get('tools_used', [])
        })
    
    except Exception as e:
        logger.error(f"Error in ai_chat_api: {str(e)}", exc_info=True)
        return JsonResponse({
            'error': 'Internal server error',
            'details': str(e)
        }, status=500)

@staff_member_required
def new_conversation(request):
    """Start a new conversation"""
    conversation = Conversation.objects.create(
        user=request.user,
        title="New Conversation"
    )
    return redirect(f'/ai-assistant/?conversation={conversation.pk}')

@staff_member_required 
def delete_conversation(request, conversation_id):
    """Delete a conversation"""
    try:
        conversation = Conversation.objects.get(id=conversation_id, user=request.user)
        conversation.delete()
    except Conversation.DoesNotExist:
        pass
    return redirect('/ai-assistant/')

def ai_status(request):
    """Simple status endpoint for AI assistant"""
    return JsonResponse({'status': 'ok', 'service': 'ai_assistant'})

@staff_member_required
def mcp_tools_api(request):
    """API endpoint to get available MCP tools"""
    try:
        # Static tool definitions - in production, these would come from MCP servers
        tools = {
            'ADVERTISING': [
                {'name': 'optimize_listing_title', 'description': 'Optimize product listing titles for better visibility', 'icon': '✨'},
                {'name': 'generate_keywords', 'description': 'Generate SEO keywords for listings', 'icon': '🔑'},
                {'name': 'create_campaign', 'description': 'Create marketing campaigns', 'icon': '📢'},
                {'name': 'analyze_competition', 'description': 'Analyze competitor listings', 'icon': '🎯'}
            ],
            'DATABASE': [
                {'name': 'search_listings', 'description': 'Search product listings in database', 'icon': '🔍'},
                {'name': 'get_analytics', 'description': 'Get sales and performance analytics', 'icon': '📊'},
                {'name': 'export_data', 'description': 'Export database records', 'icon': '📁'},
                {'name': 'run_query', 'description': 'Execute custom SQL queries', 'icon': '💾'}
            ],
            'STOCK': [
                {'name': 'check_inventory', 'description': 'Check product inventory levels', 'icon': '📦'},
                {'name': 'forecast_demand', 'description': 'Forecast product demand', 'icon': '📈'},
                {'name': 'update_stock', 'description': 'Update stock quantities', 'icon': '🔄'},
                {'name': 'reorder_alert', 'description': 'Set low stock alerts', 'icon': '⚠️'}
            ]
        }
        return JsonResponse({'tools': tools, 'status': 'success'})
    except Exception as e:
        logger.error(f"Error getting MCP tools: {e}")
        return JsonResponse({'error': str(e)}, status=500)

# Admin integration
class AIAssistantAdmin(admin.ModelAdmin):
    """Custom admin section for AI Assistant"""
    
    def get_urls(self):
        custom_urls = [
            path('ai-assistant/', self.admin_site.admin_view(ai_assistant_view)),
            path('ai-assistant/chat/', self.admin_site.admin_view(ai_chat_api)),
            path('ai-assistant/new/', self.admin_site.admin_view(new_conversation)),
            path('ai-assistant/delete/<int:conversation_id>/', self.admin_site.admin_view(delete_conversation)),
        ]
        return custom_urls + super().get_urls()
