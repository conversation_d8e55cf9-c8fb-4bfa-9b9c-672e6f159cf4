#!/usr/bin/env python3
"""
Unified MCP Addon for VSCode Commands Integration
Combines basic and advanced VSCode extension commands as MCP tools
"""

import asyncio
import json
import logging
import os
import re
import ast
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
import shutil

from mcp.server.models import InitializationOptions
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UnifiedVSCodeMCPAddon:
    """Unified MCP Addon providing comprehensive VSCode extension commands"""
    
    def __init__(self):
        self.server = Server("unified-vscode-addon")
        self.setup_tools()
        
    def setup_tools(self):
        """Setup all VSCode commands as MCP tools"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List all available VSCode extension tools"""
            return [
                Tool(
                    name="analyze_file",
                    description="Analyze file for refactoring opportunities and code quality",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "Path to the file to analyze"},
                            "focus_areas": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Areas to focus on: performance, readability, security",
                                "default": ["performance", "readability", "security"]
                            }
                        },
                        "required": ["file_path"]
                    }
                ),
                Tool(
                    name="refactor_code",
                    description="Apply intelligent code refactoring with pattern matching",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "Path to the file to refactor"},
                            "patterns": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Refactoring patterns to apply",
                                "default": ["simplify_conditionals", "optimize_imports"]
                            },
                            "auto_apply": {"type": "boolean", "default": False}
                        },
                        "required": ["file_path"]
                    }
                ),
                Tool(
                    name="security_audit",
                    description="Comprehensive security vulnerability scanning",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "Path to the file to audit"}
                        },
                        "required": ["file_path"]
                    }
                ),
                Tool(
                    name="code_metrics",
                    description="Calculate detailed code quality metrics",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "Path to the file to analyze"}
                        },
                        "required": ["file_path"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle VSCode extension tool calls"""
            
            try:
                if name == "analyze_file":
                    return await self.analyze_file(arguments)
                elif name == "refactor_code":
                    return await self.refactor_code(arguments)
                elif name == "security_audit":
                    return await self.security_audit(arguments)
                elif name == "code_metrics":
                    return await self.code_metrics(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=json.dumps({"error": str(e)}))]

    async def analyze_file(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Comprehensive file analysis"""
        file_path = arguments.get("file_path")
        focus_areas = arguments.get("focus_areas", ["performance", "readability", "security"])
        
        if not file_path or not os.path.exists(file_path):
            return [TextContent(type="text", text=json.dumps({"error": "File not found"}))]
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result = {
                "file_path": file_path,
                "file_size": len(content),
                "lines_of_code": len(content.splitlines()),
                "analysis": {},
                "timestamp": datetime.now().isoformat()
            }
            
            if "security" in focus_areas:
                result["analysis"]["security"] = self.perform_security_audit(content)
            
            if "performance" in focus_areas:
                result["analysis"]["performance"] = self.analyze_performance_issues(content)
            
            if "readability" in focus_areas:
                result["analysis"]["readability"] = self.analyze_readability(content)
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
            
        except Exception as e:
            return [TextContent(type="text", text=json.dumps({"error": str(e)}))]

    def perform_security_audit(self, content: str) -> Dict[str, List[str]]:
        """Perform security vulnerability scanning"""
        vulnerabilities = {
            "sql_injection": [],
            "hardcoded_secrets": [],
            "insecure_functions": []
        }
        
        # SQL injection patterns
        sql_patterns = [
            r'execute\s*\(\s*["\'].*%.*["\']',
            r'execute\s*\(\s*.*\+.*\+'
        ]
        
        for pattern in sql_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            vulnerabilities["sql_injection"].extend(matches)
        
        # Hardcoded secrets
        secret_patterns = [
            r'password\s*=\s*["\'][^"\']{8,}["\']',
            r'api_key\s*=\s*["\'][^"\']{20,}["\']'
        ]
        
        for pattern in secret_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            vulnerabilities["hardcoded_secrets"].extend(matches)
        
        # Insecure functions
        insecure_functions = ['eval(', 'exec(', 'os.system(']
        for func in insecure_functions:
            if func in content:
                vulnerabilities["insecure_functions"].append(func)
        
        return vulnerabilities

    def analyze_performance_issues(self, content: str) -> Dict[str, List[str]]:
        """Analyze performance issues"""
        issues = {
            "inefficient_loops": [],
            "string_concatenation": []
        }
        
        lines = content.splitlines()
        for i, line in enumerate(lines):
            if 'for ' in line and 'range(len(' in line:
                issues["inefficient_loops"].append(f"Line {i+1}: Use enumerate() instead of range(len())")
            
            if '+=' in line and 'str' in line:
                issues["string_concatenation"].append(f"Line {i+1}: Consider using join() for string concatenation")
        
        return issues

    def analyze_readability(self, content: str) -> Dict[str, Any]:
        """Analyze code readability"""
        lines = content.splitlines()
        
        return {
            "average_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0,
            "comment_ratio": len([line for line in lines if line.strip().startswith('#')]) / len(lines) if lines else 0,
            "long_lines": len([line for line in lines if len(line) > 100]),
            "max_line_length": max(len(line) for line in lines) if lines else 0
        }

    async def refactor_code(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Apply code refactoring"""
        file_path = arguments.get("file_path")
        patterns = arguments.get("patterns", ["simplify_conditionals"])
        auto_apply = arguments.get("auto_apply", False)
        
        if not file_path or not os.path.exists(file_path):
            return [TextContent(type="text", text=json.dumps({"error": "File not found"}))]
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            refactored_content = original_content
            changes = []
            
            for pattern in patterns:
                if pattern == "simplify_conditionals":
                    refactored_content, change = self.simplify_conditionals(refactored_content)
                    if change:
                        changes.append(change)
            
            result = {
                "file_path": file_path,
                "patterns_applied": patterns,
                "changes_made": len(changes),
                "changes": changes,
                "applied": False
            }
            
            if auto_apply and refactored_content != original_content:
                backup_path = f"{file_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(file_path, backup_path)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(refactored_content)
                
                result["applied"] = True
                result["backup_created"] = backup_path
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
            
        except Exception as e:
            return [TextContent(type="text", text=json.dumps({"error": str(e)}))]

    def simplify_conditionals(self, content: str) -> Tuple[str, Optional[Dict[str, Any]]]:
        """Simplify conditional statements"""
        lines = content.splitlines()
        new_lines = []
        changes_made = False
        
        for line in lines:
            original_line = line
            simplified = re.sub(r'if\s+(\w+)\s*==\s*True\s*:', r'if \1:', line)
            simplified = re.sub(r'if\s+(\w+)\s*==\s*False\s*:', r'if not \1:', simplified)
            
            if simplified != original_line:
                changes_made = True
            
            new_lines.append(simplified)
        
        change = {"type": "simplify_conditionals", "applied": changes_made} if changes_made else None
        return "\n".join(new_lines), change

    async def security_audit(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Security audit implementation"""
        file_path = arguments.get("file_path")
        
        if not file_path or not os.path.exists(file_path):
            return [TextContent(type="text", text=json.dumps({"error": "File not found"}))]
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            audit = {
                "file_path": file_path,
                "vulnerabilities": self.perform_security_audit(content),
                "recommendations": [
                    "Use parameterized queries for database operations",
                    "Avoid hardcoded secrets in code",
                    "Use environment variables for sensitive configuration"
                ]
            }
            
            return [TextContent(type="text", text=json.dumps(audit, indent=2))]
            
        except Exception as e:
            return [TextContent(type="text", text=json.dumps({"error": str(e)}))]

    async def code_metrics(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Code metrics calculation"""
        file_path = arguments.get("file_path")
        
        if not file_path or not os.path.exists(file_path):
            return [TextContent(type="text", text=json.dumps({"error": "File not found"}))]
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result = {
                "file_path": file_path,
                "metrics": {
                    "complexity": self.calculate_complexity(content),
                    "readability": self.analyze_readability(content)
                }
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
            
        except Exception as e:
            return [TextContent(type="text", text=json.dumps({"error": str(e)}))]

    def calculate_complexity(self, content: str) -> int:
        """Calculate cyclomatic complexity"""
        complexity = 1
        complexity += content.count('if ')
        complexity += content.count('elif ')
        complexity += content.count('for ')
        complexity += content.count('while ')
        complexity += content.count(' and ')
        complexity += content.count(' or ')
        return complexity


async def main():
    """Main server entry point"""
    addon = UnifiedVSCodeMCPAddon()
    
    async with stdio_server() as (read_stream, write_stream):
        await addon.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="unified-vscode-addon",
                server_version="3.0.0"
            )
        )

if __name__ == "__main__":
    asyncio.run(main())
