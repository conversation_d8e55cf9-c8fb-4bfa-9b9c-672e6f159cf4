from django.contrib import admin
from django.urls import path
from django.shortcuts import render
from django.http import JsonResponse
from django.contrib.admin.views.decorators import staff_member_required
from .models import Conversation, Message, AdminQueryLog
from .views import ai_assistant_view, ai_chat_api, new_conversation, delete_conversation
from .mcp_orchestrator import MCPOrchestrator
import asyncio
import json

@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'title', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('title', 'user__username')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'conversation', 'role', 'content_preview', 'timestamp')
    list_filter = ('role', 'timestamp')
    search_fields = ('content',)
    readonly_fields = ('timestamp',)
    
    def content_preview(self, obj):
        return obj.content[:50] + "..." if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'Content Preview'

@admin.register(AdminQueryLog)
class AdminQueryLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'sql_text_preview', 'timestamp', 'duration')
    list_filter = ('timestamp',)
    search_fields = ('sql_text', 'user__username')
    readonly_fields = ('timestamp',)
    
    def sql_text_preview(self, obj):
        return obj.sql_text[:100] + "..." if len(obj.sql_text) > 100 else obj.sql_text
    sql_text_preview.short_description = 'SQL Preview'

class AIAssistantAdmin(admin.ModelAdmin):
    """Custom admin for AI Assistant with MCP integration"""
    
    def get_urls(self):
        """Add custom URLs for AI Assistant"""
        urls = super().get_urls()
        custom_urls = [
            path('chat/', self.admin_site.admin_view(ai_assistant_view), name='ai_assistant_chat'),
            path('api/chat/', self.admin_site.admin_view(ai_chat_api), name='ai_chat_api'),
            path('api/mcp_tools/', self.admin_site.admin_view(self.mcp_tools_view), name='mcp_tools'),
            path('api/server_status/', self.admin_site.admin_view(self.server_status_view), name='server_status'),
            path('new/', self.admin_site.admin_view(new_conversation), name='new_conversation'),
            path('delete/<int:conversation_id>/', self.admin_site.admin_view(delete_conversation), name='delete_conversation'),
        ]
        return custom_urls + urls
    
    @staff_member_required
    def mcp_tools_view(self, request):
        """API endpoint to get available MCP tools"""
        try:
            orchestrator = MCPOrchestrator()
            tools = {
                'ADVERTISING': [
                    {'name': 'optimize_listing_title', 'description': 'Optimize product listing titles for better visibility', 'icon': '✨'},
                    {'name': 'generate_keywords', 'description': 'Generate SEO keywords for listings', 'icon': '🔑'},
                    {'name': 'create_campaign', 'description': 'Create marketing campaigns', 'icon': '📢'}
                ],
                'DATABASE': [
                    {'name': 'search_listings', 'description': 'Search product listings in database', 'icon': '🔍'},
                    {'name': 'get_analytics', 'description': 'Get sales and performance analytics', 'icon': '📊'},
                    {'name': 'export_data', 'description': 'Export database records', 'icon': '📁'}
                ],
                'STOCK': [
                    {'name': 'check_inventory', 'description': 'Check product inventory levels', 'icon': '📦'},
                    {'name': 'forecast_demand', 'description': 'Forecast product demand', 'icon': '📈'},
                    {'name': 'update_stock', 'description': 'Update stock quantities', 'icon': '🔄'}
                ]
            }
            return JsonResponse({'tools': tools})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    @staff_member_required
    def server_status_view(self, request):
        """API endpoint to get real MCP server status"""
        try:
            orchestrator = MCPOrchestrator()
            status = orchestrator.get_server_status()
            
            # Add real health checks
            server_health = {}
            for server_name, config in status.items():
                try:
                    import httpx
                    # Try to ping each server
                    response = httpx.get(f"{config['url']}/health", timeout=5.0)
                    server_health[server_name] = {
                        'status': 'Online' if response.status_code == 200 else 'Error',
                        'response_time': f"{response.elapsed.total_seconds():.2f}s",
                        'url': config['url'],
                        'tools_count': len(config['tools'])
                    }
                except:
                    server_health[server_name] = {
                        'status': 'Offline',
                        'response_time': 'N/A',
                        'url': config['url'],
                        'tools_count': len(config['tools'])
                    }
            
            return JsonResponse({'servers': server_health})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

# AIAssistantAdmin provides custom URLs and views but doesn't manage a specific model
# It's accessed through the custom URLs defined in get_urls() method
