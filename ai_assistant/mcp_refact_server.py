#!/usr/bin/env python3
"""
MCP Server for Refact Agent - AI-powered code refactoring
Provides tools for code analysis, refactoring suggestions, and automated improvements
"""

import asyncio
import json
import logging
import os
import re
from typing import Any, Dict, List, Optional
from pathlib import Path

from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent, ImageContent, EmbeddedResource

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RefactMCPServer:
    """MCP Server for code refactoring operations"""
    
    def __init__(self):
        self.server = Server("refact-agent")
        self.setup_tools()
        
    def setup_tools(self):
        """Setup available tools for the MCP server"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available refactoring tools"""
            return [
                Tool(
                    name="analyze_code",
                    description="Analyze code for refactoring opportunities",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "code": {
                                "type": "string",
                                "description": "Code to analyze"
                            },
                            "language": {
                                "type": "string",
                                "description": "Programming language (python, javascript, etc.)",
                                "default": "python"
                            },
                            "focus_areas": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Areas to focus on: performance, readability, security, etc.",
                                "default": ["performance", "readability", "security"]
                            }
                        },
                        "required": ["code"]
                    }
                ),
                Tool(
                    name="get_refactoring_suggestions",
                    description="Get specific refactoring suggestions for code",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "code": {
                                "type": "string",
                                "description": "Code to refactor"
                            },
                            "language": {
                                "type": "string",
                                "description": "Programming language",
                                "default": "python"
                            },
                            "suggestion_type": {
                                "type": "string",
                                "description": "Type of refactoring: performance, readability, modernize, etc.",
                                "default": "readability"
                            }
                        },
                        "required": ["code"]
                    }
                ),
                Tool(
                    name="apply_refactoring",
                    description="Apply specific refactoring changes to code",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "code": {
                                "type": "string",
                                "description": "Original code"
                            },
                            "changes": {
                                "type": "object",
                                "description": "Specific changes to apply"
                            },
                            "language": {
                                "type": "string",
                                "description": "Programming language",
                                "default": "python"
                            }
                        },
                        "required": ["code", "changes"]
                    }
                ),
                Tool(
                    name="detect_code_smells",
                    description="Detect code smells and anti-patterns",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "code": {
                                "type": "string",
                                "description": "Code to analyze"
                            },
                            "language": {
                                "type": "string",
                                "description": "Programming language",
                                "default": "python"
                            },
                            "smell_types": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Types of smells to detect",
                                "default": ["duplication", "complexity", "naming", "structure"]
                            }
                        },
                        "required": ["code"]
                    }
                ),
                Tool(
                    name="optimize_imports",
                    description="Optimize and clean up import statements",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "code": {
                                "type": "string",
                                "description": "Code with imports to optimize"
                            },
                            "language": {
                                "type": "string",
                                "description": "Programming language",
                                "default": "python"
                            }
                        },
                        "required": ["code"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            
            if name == "analyze_code":
                return await self.analyze_code(arguments)
            elif name == "get_refactoring_suggestions":
                return await self.get_refactoring_suggestions(arguments)
            elif name == "apply_refactoring":
                return await self.apply_refactoring(arguments)
            elif name == "detect_code_smells":
                return await self.detect_code_smells(arguments)
            elif name == "optimize_imports":
                return await self.optimize_imports(arguments)
            else:
                raise ValueError(f"Unknown tool: {name}")
    
    async def analyze_code(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Analyze code for refactoring opportunities"""
        code = arguments.get("code", "")
        language = arguments.get("language", "python")
        focus_areas = arguments.get("focus_areas", ["performance", "readability", "security"])
        
        analysis = {
            "language": language,
            "focus_areas": focus_areas,
            "issues": [],
            "suggestions": []
        }
        
        # Basic analysis based on language
        if language.lower() == "python":
            analysis["issues"] = self._analyze_python_code(code)
        elif language.lower() == "javascript":
            analysis["issues"] = self._analyze_javascript_code(code)
        
        return [TextContent(
            type="text",
            text=json.dumps(analysis, indent=2)
        )]
    
    async def get_refactoring_suggestions(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get specific refactoring suggestions"""
        code = arguments.get("code", "")
        language = arguments.get("language", "python")
        suggestion_type = arguments.get("suggestion_type", "readability")
        
        suggestions = {
            "original_code": code,
            "suggestion_type": suggestion_type,
            "suggestions": []
        }
        
        # Generate suggestions based on type
        if suggestion_type == "performance":
            suggestions["suggestions"] = self._get_performance_suggestions(code, language)
        elif suggestion_type == "readability":
            suggestions["suggestions"] = self._get_readability_suggestions(code, language)
        elif suggestion_type == "modernize":
            suggestions["suggestions"] = self._get_modernization_suggestions(code, language)
        
        return [TextContent(
            type="text",
            text=json.dumps(suggestions, indent=2)
        )]
    
    async def apply_refactoring(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Apply specific refactoring changes to code"""
        code = arguments.get("code", "")
        changes = arguments.get("changes", {})
        language = arguments.get("language", "python")
        
        refactored_code = self._apply_changes(code, changes, language)
        
        result = {
            "original_code": code,
            "changes_applied": changes,
            "refactored_code": refactored_code
        }
        
        return [TextContent(
            type="text",
            text=json.dumps(result, indent=2)
        )]
    
    async def detect_code_smells(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Detect code smells and anti-patterns"""
        code = arguments.get("code", "")
        language = arguments.get("language", "python")
        smell_types = arguments.get("smell_types", ["duplication", "complexity", "naming", "structure"])
        
        smells = self._detect_smells(code, language, smell_types)
        
        return [TextContent(
            type="text",
            text=json.dumps({"smells": smells}, indent=2)
        )]
    
    async def optimize_imports(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Optimize and clean up import statements"""
        code = arguments.get("code", "")
        language = arguments.get("language", "python")
        
        optimized = self._optimize_imports(code, language)
        
        return [TextContent(
            type="text",
            text=json.dumps({"optimized_code": optimized}, indent=2)
        )]
    
    def _analyze_python_code(self, code: str) -> List[Dict[str, Any]]:
        """Analyze Python code for issues"""
        issues = []
        
        # Check for long functions
        lines = code.split('\n')
        function_lines = []
        in_function = False
        indent_level = 0
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('def ') and not in_function:
                in_function = True
                function_lines = [i]
                indent_level = len(line) - len(line.lstrip())
            elif in_function and line.strip() and (len(line) - len(line.lstrip())) <= indent_level:
                if len(function_lines) > 20:  # Long function
                    issues.append({
                        "type": "complexity",
                        "line": function_lines[0] + 1,
                        "message": f"Function too long ({len(function_lines)} lines)",
                        "severity": "medium"
                    })
                in_function = False
        
        # Check for unused variables (basic pattern)
        if re.search(r'\b_\w*\s*=\s*.*\n', code):
            issues.append({
                "type": "style",
                "line": 1,
                "message": "Unused variables detected",
                "severity": "low"
            })
        
        # Check for print statements
        if 'print(' in code:
            issues.append({
                "type": "style",
                "line": 1,
                "message": "Consider using logging instead of print statements",
                "severity": "low"
            })
        
        return issues
    
    def _analyze_javascript_code(self, code: str) -> List[Dict[str, Any]]:
        """Analyze JavaScript code for issues"""
        issues = []
        
        # Check for var usage
        if 'var ' in code:
            issues.append({
                "type": "modernization",
                "line": 1,
                "message": "Use 'let' or 'const' instead of 'var'",
                "severity": "low"
            })
        
        # Check for console.log
        if 'console.log' in code:
            issues.append({
                "type": "style",
                "line": 1,
                "message": "Remove console.log statements",
                "severity": "low"
            })
        
        return issues
    
    def _get_performance_suggestions(self, code: str, language: str) -> List[Dict[str, Any]]:
        """Get performance improvement suggestions"""
        suggestions = []
        
        if language.lower() == "python":
            # Suggest list comprehensions
            if re.search(r'for.*in.*:\s*\w+\.append\(', code):
                suggestions.append({
                    "type": "performance",
                    "description": "Use list comprehension for better performance",
                    "example": "[x for x in items if condition]"
                })
            
            # Suggest set for membership tests
            if re.search(r'if.*in\s*\[.*\]', code):
                suggestions.append({
                    "type": "performance",
                    "description": "Use set for O(1) membership tests",
                    "example": "items_set = set(items)"
                })
        
        return suggestions
    
    def _get_readability_suggestions(self, code: str, language: str) -> List[Dict[str, Any]]:
        """Get readability improvement suggestions"""
        suggestions = []
        
        # Suggest better variable names
        if re.search(r'\b[a-z][a-z0-9]*[A-Z]', code):
            suggestions.append({
                "type": "readability",
                "description": "Use snake_case for variable names",
                "example": "user_name instead of userName"
            })
        
        # Suggest function documentation
        if language.lower() == "python" and re.search(r'def\s+\w+\s*\([^)]*\):', code):
            suggestions.append({
                "type": "readability",
                "description": "Add docstrings to functions",
                "example": 'def function():\n    """Description of function"""'
            })
        
        return suggestions
    
    def _get_modernization_suggestions(self, code: str, language: str) -> List[Dict[str, Any]]:
        """Get modernization suggestions"""
        suggestions = []
        
        if language.lower() == "python":
            # Suggest f-strings
            if re.search(r'["\'].*%.*["\']', code) or re.search(r'["\'].*\.format\(', code):
                suggestions.append({
                    "type": "modernization",
                    "description": "Use f-strings for string formatting",
                    "example": 'f"Hello {name}"'
                })
        
        return suggestions
    
    def _detect_smells(self, code: str, language: str, smell_types: List[str]) -> List[Dict[str, Any]]:
        """Detect code smells"""
        smells = []
        
        # Duplicate code detection (basic)
        if "duplication" in smell_types:
            lines = code.split('\n')
            seen = set()
            for i, line in enumerate(lines):
                stripped = line.strip()
                if stripped and stripped in seen:
                    smells.append({
                        "type": "duplication",
                        "line": i + 1,
                        "message": "Duplicate code detected",
                        "severity": "medium"
                    })
                seen.add(stripped)
        
        # Long parameter list
        if "complexity" in smell_types and language.lower() == "python":
            matches = re.findall(r'def\s+\w+\s*\(([^)]*)\)', code)
            for match in matches:
                params = match.split(',')
                if len(params) > 4:
                    smells.append({
                        "type": "complexity",
                        "message": f"Function has too many parameters ({len(params)})",
                        "severity": "medium"
                    })
        
        return smells
    
    def _optimize_imports(self, code: str, language: str) -> str:
        """Optimize import statements"""
        if language.lower() != "python":
            return code
        
        lines = code.split('\n')
        imports = []
        other_lines = []
        
        for line in lines:
            if line.strip().startswith(('import ', 'from ')):
                imports.append(line.strip())
            else:
                other_lines.append(line)
        
        # Sort imports
        imports.sort()
        
        # Remove duplicates
        unique_imports = list(dict.fromkeys(imports))
        
        # Reconstruct code
        optimized_code = '\n'.join(unique_imports + [''] + other_lines)
        return optimized_code
    
    def _apply_changes(self, code: str, changes: Dict[str, Any], language: str) -> str:
        """Apply refactoring changes to code"""
        # This is a simplified implementation
        # In a real scenario, this would use AST manipulation
        
        modified_code = code
        
        # Apply string replacements
        if "replacements" in changes:
            for replacement in changes["replacements"]:
                old = replacement.get("old", "")
                new = replacement.get("new", "")
                modified_code = modified_code.replace(old, new)
        
        return modified_code

async def main():
    """Main server entry point"""
    server = RefactMCPServer()
    
    async with stdio_server() as (read_stream, write_stream):
        await server.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="refact-agent",
                server_version="1.0.0",
                capabilities=server.server.get_capabilities()
            )
        )

if __name__ == "__main__":
    asyncio.run(main())
