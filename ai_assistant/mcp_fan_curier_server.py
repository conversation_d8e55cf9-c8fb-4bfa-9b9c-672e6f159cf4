#!/usr/bin/env python3
"""
MCP Server for Fan-Curier Integration
Provides comprehensive shipping and logistics tools for Romanian marketplace
"""

import asyncio
import json
import logging
import os
import requests
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from pathlib import Path

from mcp.server.models import InitializationOptions
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FanCurierMCPServer:
    """MCP Server for Fan-Curier shipping integration"""
    
    def __init__(self):
        self.server = Server("fan-curier-agent")
        self.api_key = os.getenv('FAN_CURIER_API_KEY')
        self.client_id = os.getenv('FAN_CURIER_CLIENT_ID')
        self.base_url = "https://api.fancourier.ro/v1"
        self.setup_tools()
        
    def setup_tools(self):
        """Setup Fan-Curier shipping tools"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available Fan-Curier tools"""
            return [
                Tool(
                    name="create_shipment",
                    description="Create a new shipment with Fan-Curier",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "sender_data": {
                                "type": "object",
                                "description": "Sender information",
                                "properties": {
                                    "name": {"type": "string"},
                                    "phone": {"type": "string"},
                                    "email": {"type": "string"},
                                    "address": {"type": "string"},
                                    "locality": {"type": "string"},
                                    "county": {"type": "string"},
                                    "postal_code": {"type": "string"}
                                },
                                "required": ["name", "phone", "address", "locality", "county"]
                            },
                            "recipient_data": {
                                "type": "object",
                                "description": "Recipient information",
                                "properties": {
                                    "name": {"type": "string"},
                                    "phone": {"type": "string"},
                                    "email": {"type": "string"},
                                    "address": {"type": "string"},
                                    "locality": {"type": "string"},
                                    "county": {"type": "string"},
                                    "postal_code": {"type": "string"}
                                },
                                "required": ["name", "phone", "address", "locality", "county"]
                            },
                            "package_data": {
                                "type": "object",
                                "description": "Package details",
                                "properties": {
                                    "weight": {"type": "number", "description": "Weight in kg"},
                                    "length": {"type": "number", "description": "Length in cm"},
                                    "width": {"type": "number", "description": "Width in cm"},
                                    "height": {"type": "number", "description": "Height in cm"},
                                    "declared_value": {"type": "number", "description": "Value in RON"},
                                    "contents": {"type": "string", "description": "Package contents"},
                                    "fragile": {"type": "boolean", "default": False}
                                },
                                "required": ["weight", "declared_value", "contents"]
                            },
                            "service_type": {
                                "type": "string",
                                "enum": ["standard", "express", "same_day", "economy"],
                                "default": "standard"
                            },
                            "payment_type": {
                                "type": "string",
                                "enum": ["sender", "recipient", "third_party"],
                                "default": "sender"
                            },
                            "cash_on_delivery": {
                                "type": "number",
                                "description": "COD amount in RON",
                                "default": 0
                            }
                        },
                        "required": ["sender_data", "recipient_data", "package_data"]
                    }
                ),
                Tool(
                    name="track_shipment",
                    description="Track shipment status and get delivery updates",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "awb_number": {
                                "type": "string",
                                "description": "AWB tracking number"
                            },
                            "detailed": {
                                "type": "boolean",
                                "description": "Get detailed tracking history",
                                "default": False
                            }
                        },
                        "required": ["awb_number"]
                    }
                ),
                Tool(
                    name="calculate_shipping_cost",
                    description="Calculate shipping cost for a package",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "origin_locality": {"type": "string"},
                            "destination_locality": {"type": "string"},
                            "weight": {"type": "number", "description": "Weight in kg"},
                            "declared_value": {"type": "number", "description": "Value in RON"},
                            "service_type": {
                                "type": "string",
                                "enum": ["standard", "express", "same_day", "economy"],
                                "default": "standard"
                            },
                            "cash_on_delivery": {"type": "number", "default": 0}
                        },
                        "required": ["origin_locality", "destination_locality", "weight"]
                    }
                ),
                Tool(
                    name="get_localities",
                    description="Get list of available localities for shipping",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "county": {
                                "type": "string",
                                "description": "Filter by county (județ)"
                            },
                            "search": {
                                "type": "string",
                                "description": "Search localities by name"
                            }
                        }
                    }
                ),
                Tool(
                    name="schedule_pickup",
                    description="Schedule package pickup from seller",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "pickup_address": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "phone": {"type": "string"},
                                    "address": {"type": "string"},
                                    "locality": {"type": "string"},
                                    "county": {"type": "string"}
                                },
                                "required": ["name", "phone", "address", "locality", "county"]
                            },
                            "pickup_date": {
                                "type": "string",
                                "description": "Pickup date (YYYY-MM-DD)"
                            },
                            "time_interval": {
                                "type": "string",
                                "description": "Time interval (e.g., 09:00-17:00)",
                                "default": "09:00-17:00"
                            },
                            "packages_count": {
                                "type": "integer",
                                "description": "Number of packages",
                                "default": 1
                            }
                        },
                        "required": ["pickup_address", "pickup_date"]
                    }
                ),
                Tool(
                    name="cancel_shipment",
                    description="Cancel an existing shipment",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "awb_number": {
                                "type": "string",
                                "description": "AWB number to cancel"
                            },
                            "reason": {
                                "type": "string",
                                "description": "Cancellation reason"
                            }
                        },
                        "required": ["awb_number"]
                    }
                ),
                Tool(
                    name="get_delivery_proof",
                    description="Get proof of delivery for completed shipments",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "awb_number": {
                                "type": "string",
                                "description": "AWB tracking number"
                            }
                        },
                        "required": ["awb_number"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle Fan-Curier tool calls"""
            
            try:
                if name == "create_shipment":
                    result = await self.create_shipment(arguments)
                elif name == "track_shipment":
                    result = await self.track_shipment(arguments)
                elif name == "calculate_shipping_cost":
                    result = await self.calculate_shipping_cost(arguments)
                elif name == "get_localities":
                    result = await self.get_localities(arguments)
                elif name == "schedule_pickup":
                    result = await self.schedule_pickup(arguments)
                elif name == "cancel_shipment":
                    result = await self.cancel_shipment(arguments)
                elif name == "get_delivery_proof":
                    result = await self.get_delivery_proof(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
                
                return [TextContent(type="text", text=json.dumps(result, indent=2))]
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=json.dumps({"error": str(e)}))]

    async def create_shipment(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new shipment with Fan-Curier"""
        sender_data = arguments.get("sender_data")
        recipient_data = arguments.get("recipient_data")
        package_data = arguments.get("package_data")
        service_type = arguments.get("service_type", "standard")
        payment_type = arguments.get("payment_type", "sender")
        cash_on_delivery = arguments.get("cash_on_delivery", 0)
        
        # Validate required data
        if not sender_data or not recipient_data or not package_data:
            return {"success": False, "error": "Missing required shipment data"}
        
        # Prepare shipment data for Fan-Curier API
        shipment_data = {
            "service": self._get_service_code(service_type),
            "bank": payment_type,
            "sender": {
                "name": sender_data.get("name", ""),
                "phone": sender_data.get("phone", ""),
                "email": sender_data.get("email", ""),
                "address": sender_data.get("address", ""),
                "locality": sender_data.get("locality", ""),
                "county": sender_data.get("county", ""),
                "postal_code": sender_data.get("postal_code", "")
            },
            "recipient": {
                "name": recipient_data.get("name", ""),
                "phone": recipient_data.get("phone", ""),
                "email": recipient_data.get("email", ""),
                "address": recipient_data.get("address", ""),
                "locality": recipient_data.get("locality", ""),
                "county": recipient_data.get("county", ""),
                "postal_code": recipient_data.get("postal_code", "")
            },
            "packages": [{
                "weight": package_data.get("weight", 0),
                "length": package_data.get("length", 0),
                "width": package_data.get("width", 0),
                "height": package_data.get("height", 0),
                "declared_value": package_data.get("declared_value", 0),
                "contents": package_data.get("contents", ""),
                "fragile": package_data.get("fragile", False)
            }],
            "cash_on_delivery": cash_on_delivery,
            "observations": f"Marketplace order - {package_data.get('contents', 'Unknown item')}"
        }
        
        try:
            # Simulate API call (replace with actual Fan-Curier API integration)
            awb_number = await self._simulate_create_shipment(shipment_data)
            
            result = {
                "success": True,
                "awb_number": awb_number,
                "service_type": service_type,
                "estimated_delivery": self._calculate_estimated_delivery(service_type),
                "tracking_url": f"https://www.fancourier.ro/awb-tracking/?awb={awb_number}",
                "cost_estimate": await self._estimate_cost(
                    sender_data["locality"],
                    recipient_data["locality"],
                    package_data["weight"],
                    service_type
                ),
                "shipment_data": shipment_data
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error creating shipment: {str(e)}")
            return {"success": False, "error": str(e)}

    async def track_shipment(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Track shipment status"""
        awb_number = arguments.get("awb_number")
        detailed = arguments.get("detailed", False)
        
        if not awb_number:
            return {"error": "AWB number is required"}
        
        try:
            # Simulate tracking data (replace with actual API call)
            tracking_data = await self._simulate_tracking(str(awb_number), detailed)
            
            return {
                "awb_number": awb_number,
                "status": tracking_data["status"],
                "current_location": tracking_data["location"],
                "estimated_delivery": tracking_data["estimated_delivery"],
                "last_update": tracking_data["last_update"],
                "tracking_history": tracking_data.get("history", []) if detailed else [],
                "delivery_attempts": tracking_data.get("delivery_attempts", 0),
                "recipient_notifications": tracking_data.get("notifications", [])
            }
            
        except Exception as e:
            logger.error(f"Error tracking shipment: {str(e)}")
            return {"error": str(e)}

    async def calculate_shipping_cost(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate shipping cost"""
        origin = arguments.get("origin_locality")
        destination = arguments.get("destination_locality")
        weight = arguments.get("weight")
        declared_value = arguments.get("declared_value", 0)
        service_type = arguments.get("service_type", "standard")
        cod_amount = arguments.get("cash_on_delivery", 0)
        
        # Validate required parameters
        if not origin or not destination or weight is None:
            return {"error": "Missing required parameters: origin_locality, destination_locality, weight"}
        
        try:
            cost_data = await self._calculate_cost(
                str(origin), 
                str(destination), 
                float(weight), 
                str(service_type), 
                float(declared_value), 
                float(cod_amount)
            )
            
            return {
                "origin": origin,
                "destination": destination,
                "weight": weight,
                "service_type": service_type,
                "base_cost": cost_data["base_cost"],
                "insurance_cost": cost_data["insurance_cost"],
                "cod_fee": cost_data["cod_fee"],
                "total_cost": cost_data["total_cost"],
                "currency": "RON",
                "estimated_delivery_days": cost_data["delivery_days"],
                "cost_breakdown": cost_data["breakdown"]
            }
            
        except Exception as e:
            logger.error(f"Error calculating cost: {str(e)}")
            return {"error": str(e)}

    async def get_localities(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Get available localities"""
        county = arguments.get("county")
        search = arguments.get("search")
        
        # Romanian localities database (simplified)
        localities_db = await self._get_localities_database()
        
        filtered_localities = localities_db
        
        if county:
            filtered_localities = [loc for loc in filtered_localities if loc["county"].lower() == county.lower()]
        
        if search:
            search_term = search.lower()
            filtered_localities = [loc for loc in filtered_localities if search_term in loc["name"].lower()]
        
        return {
            "localities": filtered_localities[:100],  # Limit results
            "total_count": len(filtered_localities),
            "filters_applied": {
                "county": county,
                "search": search
            }
        }

    async def schedule_pickup(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Schedule package pickup"""
        pickup_address = arguments.get("pickup_address")
        pickup_date = arguments.get("pickup_date")
        time_interval = arguments.get("time_interval", "09:00-17:00")
        packages_count = arguments.get("packages_count", 1)
        
        # Validate required parameters
        if not pickup_address or not pickup_date:
            return {"success": False, "error": "Missing required parameters: pickup_address, pickup_date"}
        
        try:
            pickup_id = await self._simulate_schedule_pickup(
                pickup_address if isinstance(pickup_address, dict) else {}, 
                str(pickup_date), 
                str(time_interval)
            )
            
            return {
                "success": True,
                "pickup_id": pickup_id,
                "pickup_date": pickup_date,
                "time_interval": time_interval,
                "pickup_address": pickup_address,
                "packages_count": packages_count,
                "confirmation_code": f"FC{pickup_id}",
                "contact_phone": "+40 21 9999 (Fan-Curier)"
            }
            
        except Exception as e:
            logger.error(f"Error scheduling pickup: {str(e)}")
            return {"success": False, "error": str(e)}

    async def cancel_shipment(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Cancel shipment"""
        awb_number = arguments.get("awb_number")
        reason = arguments.get("reason", "Customer request")
        
        if not awb_number:
            return {"success": False, "error": "AWB number is required"}
        
        try:
            success = await self._simulate_cancel_shipment(str(awb_number), str(reason))
            
            if success:
                return {
                    "success": True,
                    "awb_number": awb_number,
                    "cancellation_reason": reason,
                    "cancelled_at": datetime.now().isoformat(),
                    "refund_eligible": True
                }
            else:
                return {
                    "success": False,
                    "error": "Shipment cannot be cancelled (already in transit or delivered)"
                }
                
        except Exception as e:
            logger.error(f"Error cancelling shipment: {str(e)}")
            return {"success": False, "error": str(e)}

    async def get_delivery_proof(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Get proof of delivery"""
        awb_number = arguments.get("awb_number")
        
        if not awb_number:
            return {"error": "AWB number is required"}
        
        try:
            proof_data = await self._simulate_delivery_proof(str(awb_number))
            
            return {
                "awb_number": awb_number,
                "delivery_status": proof_data["status"],
                "delivered_at": proof_data["delivered_at"],
                "recipient_name": proof_data["recipient_name"],
                "signature_available": proof_data["signature_available"],
                "photo_proof": proof_data["photo_proof"],
                "delivery_location": proof_data["location"],
                "courier_name": proof_data["courier_name"]
            }
            
        except Exception as e:
            logger.error(f"Error getting delivery proof: {str(e)}")
            return {"error": str(e)}

    # Helper methods for API simulation and data processing
    
    def _get_service_code(self, service_type: str) -> str:
        """Map service type to Fan-Curier service code"""
        service_map = {
            "standard": "Standard",
            "express": "Express",
            "same_day": "SameDay",
            "economy": "Economy"
        }
        return service_map.get(service_type, "Standard")

    def _calculate_estimated_delivery(self, service_type: str) -> str:
        """Calculate estimated delivery date"""
        days_map = {
            "same_day": 0,
            "express": 1,
            "standard": 2,
            "economy": 3
        }
        
        days = days_map.get(service_type, 2)
        delivery_date = datetime.now() + timedelta(days=days)
        return delivery_date.strftime("%Y-%m-%d")

    async def _estimate_cost(self, origin: str, destination: str, weight: float, service_type: str) -> float:
        """Estimate shipping cost"""
        base_rates = {
            "same_day": 25.0,
            "express": 15.0,
            "standard": 10.0,
            "economy": 8.0
        }
        
        base_cost = base_rates.get(service_type, 10.0)
        weight_cost = weight * 2.0  # 2 RON per kg
        
        # Distance factor (simplified)
        distance_factor = 1.0
        if origin.lower() != destination.lower():
            distance_factor = 1.5
        
        return round((base_cost + weight_cost) * distance_factor, 2)

    async def _simulate_create_shipment(self, shipment_data: Dict[str, Any]) -> str:
        """Simulate shipment creation (replace with actual API call)"""
        import random
        import string
        
        # Generate AWB number
        awb_number = ''.join(random.choices(string.digits, k=10))
        
        # Log shipment creation
        logger.info(f"Created shipment: AWB {awb_number}")
        
        return awb_number

    async def _simulate_tracking(self, awb_number: str, detailed: bool) -> Dict[str, Any]:
        """Simulate tracking data"""
        statuses = ["Picked up", "In transit", "Out for delivery", "Delivered"]
        locations = ["București", "Cluj-Napoca", "Timișoara", "Destination city"]
        
        import random
        status_index = random.randint(0, len(statuses) - 1)
        
        tracking_data = {
            "status": statuses[status_index],
            "location": locations[min(status_index, len(locations) - 1)],
            "estimated_delivery": self._calculate_estimated_delivery("standard"),
            "last_update": datetime.now().isoformat(),
            "delivery_attempts": 0 if status_index < 3 else random.randint(0, 2)
        }
        
        if detailed:
            tracking_data["history"] = [
                {
                    "timestamp": (datetime.now() - timedelta(hours=i*6)).isoformat(),
                    "status": statuses[min(i, len(statuses) - 1)],
                    "location": locations[min(i, len(locations) - 1)]
                }
                for i in range(status_index + 1)
            ]
            tracking_data["notifications"] = [
                "SMS sent to recipient",
                "Email notification sent"
            ]
        
        return tracking_data

    async def _calculate_cost(self, origin: str, destination: str, weight: float, 
                            service_type: str, declared_value: float, cod_amount: float) -> Dict[str, Any]:
        """Calculate detailed shipping cost"""
        base_cost = await self._estimate_cost(origin, destination, weight, service_type)
        
        # Insurance cost (0.5% of declared value, minimum 2 RON)
        insurance_cost = max(declared_value * 0.005, 2.0) if declared_value > 0 else 0
        
        # COD fee (2% of COD amount, minimum 5 RON)
        cod_fee = max(cod_amount * 0.02, 5.0) if cod_amount > 0 else 0
        
        total_cost = base_cost + insurance_cost + cod_fee
        
        delivery_days_map = {
            "same_day": 0,
            "express": 1,
            "standard": 2,
            "economy": 3
        }
        
        return {
            "base_cost": base_cost,
            "insurance_cost": insurance_cost,
            "cod_fee": cod_fee,
            "total_cost": round(total_cost, 2),
            "delivery_days": delivery_days_map.get(service_type, 2),
            "breakdown": {
                "transport": base_cost,
                "insurance": insurance_cost,
                "cod_service": cod_fee,
                "vat": round(total_cost * 0.19, 2)
            }
        }

    async def _get_localities_database(self) -> List[Dict[str, str]]:
        """Get Romanian localities database"""
        # Simplified localities database
        return [
            {"name": "București", "county": "București", "postal_code": "010001"},
            {"name": "Cluj-Napoca", "county": "Cluj", "postal_code": "400001"},
            {"name": "Timișoara", "county": "Timiș", "postal_code": "300001"},
            {"name": "Iași", "county": "Iași", "postal_code": "700001"},
            {"name": "Constanța", "county": "Constanța", "postal_code": "900001"},
            {"name": "Craiova", "county": "Dolj", "postal_code": "200001"},
            {"name": "Brașov", "county": "Brașov", "postal_code": "500001"},
            {"name": "Galați", "county": "Galați", "postal_code": "800001"},
            {"name": "Ploiești", "county": "Prahova", "postal_code": "100001"},
            {"name": "Oradea", "county": "Bihor", "postal_code": "410001"},
            # Add more localities as needed
        ]

    async def _simulate_schedule_pickup(self, pickup_address: Dict[str, Any], 
                                      pickup_date: str, time_interval: str) -> str:
        """Simulate pickup scheduling"""
        import random
        pickup_id = f"PU{random.randint(100000, 999999)}"
        logger.info(f"Scheduled pickup: {pickup_id} for {pickup_date}")
        return pickup_id

    async def _simulate_cancel_shipment(self, awb_number: str, reason: str) -> bool:
        """Simulate shipment cancellation"""
        # Simulate cancellation logic
        logger.info(f"Cancelled shipment: {awb_number}, reason: {reason}")
        return True

    async def _simulate_delivery_proof(self, awb_number: str) -> Dict[str, Any]:
        """Simulate delivery proof data"""
        return {
            "status": "Delivered",
            "delivered_at": (datetime.now() - timedelta(hours=2)).isoformat(),
            "recipient_name": "Ion Popescu",
            "signature_available": True,
            "photo_proof": True,
            "location": "Recipient address",
            "courier_name": "Mihai Ionescu"
        }

async def main():
    """Main server entry point"""
    server = FanCurierMCPServer()
    
    async with stdio_server() as (read_stream, write_stream):
        await server.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="fan-curier-agent",
                server_version="1.0.0",
                capabilities=server.server.get_capabilities()
            )
        )

if __name__ == "__main__":
    asyncio.run(main())
