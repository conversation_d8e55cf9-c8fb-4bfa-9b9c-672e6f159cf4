"""
VSCode Extension Integration for Piața.ro AI Assistant
Provides seamless integration between VSCode and the AI assistant
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, List
import requests
from datetime import datetime

class VSCodeAIIntegration:
    """Integration class for VSCode extension"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def analyze_current_file(self, file_path: str) -> Dict[str, Any]:
        """Analyze the current file for refactoring opportunities"""
        try:
            with open(file_path, 'r') as f:
                code = f.read()
            
            # Determine language from extension
            extension = Path(file_path).suffix.lower()
            language_map = {
                '.py': 'python',
                '.js': 'javascript',
                '.ts': 'typescript',
                '.jsx': 'javascript',
                '.tsx': 'typescript',
                '.java': 'java',
                '.cpp': 'cpp',
                '.c': 'c',
                '.go': 'go',
                '.rs': 'rust'
            }
            language = language_map.get(extension, 'python')
            
            # Call MCP server for analysis
            response = self.session.post(
                f"{self.base_url}/ai-assistant/mcp/analyze",
                json={
                    "code": code,
                    "language": language,
                    "focus_areas": ["performance", "readability", "security"]
                }
            )
            
            return response.json()
            
        except Exception as e:
            return {"error": str(e)}
    
    def get_refactoring_suggestions(self, file_path: str, suggestion_type: str = "readability") -> Dict[str, Any]:
        """Get refactoring suggestions for current file"""
        try:
            with open(file_path, 'r') as f:
                code = f.read()
            
            extension = Path(file_path).suffix.lower()
            language = 'python' if extension == '.py' else 'javascript'
            
            response = self.session.post(
                f"{self.base_url}/ai-assistant/mcp/suggestions",
                json={
                    "code": code,
                    "language": language,
                    "suggestion_type": suggestion_type
                }
            )
            
            return response.json()
            
        except Exception as e:
            return {"error": str(e)}
    
    def apply_refactoring(self, file_path: str, changes: Dict[str, Any]) -> Dict[str, Any]:
        """Apply refactoring changes to current file"""
        try:
            with open(file_path, 'r') as f:
                code = f.read()
            
            extension = Path(file_path).suffix.lower()
            language = 'python' if extension == '.py' else 'javascript'
            
            response = self.session.post(
                f"{self.base_url}/ai-assistant/mcp/apply",
                json={
                    "code": code,
                    "changes": changes,
                    "language": language
                }
            )
            
            result = response.json()
            if "refactored_code" in result:
                # Write back to file
                with open(file_path, 'w') as f:
                    f.write(result["refactored_code"])
                    
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    def detect_code_smells(self, file_path: str) -> Dict[str, Any]:
        """Detect code smells in current file"""
        try:
            with open(file_path, 'r') as f:
                code = f.read()
            
            extension = Path(file_path).suffix.lower()
            language = 'python' if extension == '.py' else 'javascript'
            
            response = self.session.post(
                f"{self.base_url}/ai-assistant/mcp/smells",
                json={
                    "code": code,
                    "language": language
                }
            )
            
            return response.json()
            
        except Exception as e:
            return {"error": str(e)}
    
    def optimize_imports(self, file_path: str) -> Dict[str, Any]:
        """Optimize imports in current file"""
        try:
            with open(file_path, 'r') as f:
                code = f.read()
            
            extension = Path(file_path).suffix.lower()
            language = 'python' if extension == '.py' else 'javascript'
            
            response = self.session.post(
                f"{self.base_url}/ai-assistant/mcp/optimize-imports",
                json={
                    "code": code,
                    "language": language
                }
            )
            
            result = response.json()
            if "optimized_code" in result:
                # Write back to file
                with open(file_path, 'w') as f:
                    f.write(result["optimized_code"])
                    
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    def create_marketing_campaign(self, platform: str, product_info: Dict[str, Any]) -> Dict[str, Any]:
        """Create marketing campaign for marketplace listings"""
        try:
            response = self.session.post(
                f"{self.base_url}/ai-assistant/marketing/campaign",
                json={
                    "platform": platform,
                    "product_info": product_info,
                    "campaign_type": "conversion"
                }
            )
            
            return response.json()
            
        except Exception as e:
            return {"error": str(e)}
    
    def get_marketing_insights(self, platform: str, timeframe: str = "7d") -> Dict[str, Any]:
        """Get marketing insights for platform"""
        try:
            response = self.session.get(
                f"{self.base_url}/ai-assistant/marketing/insights",
                params={
                    "platform": platform,
                    "timeframe": timeframe
                }
            )
            
            return response.json()
            
        except Exception as e:
            return {"error": str(e)}

# VSCode Extension Commands
class VSCodeCommands:
    """Available VSCode extension commands"""
    
    @staticmethod
    def analyze_file(file_path: str):
        """Analyze current file"""
        integration = VSCodeAIIntegration()
        return integration.analyze_current_file(file_path)
    
    @staticmethod
    def get_suggestions(file_path: str, suggestion_type: str = "readability"):
        """Get refactoring suggestions"""
        integration = VSCodeAIIntegration()
        return integration.get_refactoring_suggestions(file_path, suggestion_type)
    
    @staticmethod
    def apply_changes(file_path: str, changes: Dict[str, Any]):
        """Apply refactoring changes"""
        integration = VSCodeAIIntegration()
        return integration.apply_refactoring(file_path, changes)
    
    @staticmethod
    def detect_smells(file_path: str):
        """Detect code smells"""
        integration = VSCodeAIIntegration()
        return integration.detect_code_smells(file_path)
    
    @staticmethod
    def optimize_imports(file_path: str):
        """Optimize imports in current file"""
        integration = VSCodeAIIntegration()
        return integration.optimize_imports(file_path)

# CLI Interface for direct usage
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='VSCode AI Integration CLI')
    parser.add_argument('command', choices=['analyze', 'suggest', 'apply', 'smells', 'optimize'])
    parser.add_argument('file_path', help='Path to the file to process')
    parser.add_argument('--type', default='readability', help='Suggestion type')
    parser.add_argument('--changes', help='JSON string of changes to apply')
    
    args = parser.parse_args()
    
    result = None
    if args.command == 'analyze':
        result = VSCodeCommands.analyze_file(args.file_path)
    elif args.command == 'suggest':
        result = VSCodeCommands.get_suggestions(args.file_path, args.type)
    elif args.command == 'apply':
        changes = json.loads(args.changes) if args.changes else {}
        result = VSCodeCommands.apply_changes(args.file_path, changes)
    elif args.command == 'smells':
        result = VSCodeCommands.detect_smells(args.file_path)
    elif args.command == 'optimize':
        result = VSCodeCommands.optimize_imports(args.file_path)
    
    if result:
        print(json.dumps(result, indent=2))
    else:
        print(json.dumps({"error": "Invalid command"}, indent=2))
