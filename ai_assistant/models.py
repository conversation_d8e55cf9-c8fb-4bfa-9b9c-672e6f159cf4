from django.db import models
from django.contrib.auth.models import User
import json


def get_default_user():
    return User.objects.first()

class Conversation(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, default=get_default_user)
    title = models.CharField(max_length=100, blank=True, default='')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Conversation {self.id} - {self.title or 'No title'}"

class Message(models.Model):
    conversation = models.ForeignKey('Conversation', related_name='messages', on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=[('user', 'User'), ('assistant', 'Assistant')], default='user')
    content = models.TextField()
    mcp_tools_used = models.JSONField(default=list, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # Legacy fields for backward compatibility
    @property
    def text(self):
        return self.content
    
    @property
    def is_user(self):
        return self.role == 'user'
    
    def __str__(self):
        return f"{self.role}: {self.content[:50]}..."

class AdminQueryLog(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    sql_text = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    duration = models.FloatField()
    
    def __str__(self):
        return f"Query by {self.user.username} at {self.timestamp}"
