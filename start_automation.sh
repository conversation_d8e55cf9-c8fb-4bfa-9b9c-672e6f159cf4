#!/bin/bash
# Script to start the automation stack (N8N and Flowise)

set -e

echo "🚀 Starting Automation Stack (N8N & Flowise)..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Build and start services
echo "🔨 Building and starting Docker containers..."
docker-compose -f docker-compose.automation.yml up -d

echo ""
echo "✅ Automation stack started successfully!"
echo ""
echo "🌐 Your services are now running at:"
echo "   - N8N:      http://localhost:5678"
echo "   - Flowise:  http://localhost:3000"
echo ""
echo "📊 To see the status, run: docker-compose -f docker-compose.automation.yml ps"
echo "🛑 To stop, run: docker-compose -f docker-compose.automation.yml down