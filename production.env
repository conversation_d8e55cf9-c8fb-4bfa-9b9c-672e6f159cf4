# Production Environment Configuration
DEBUG=False
SECRET_KEY=CHANGE-THIS-TO-A-STRONG-SECRET-KEY-IN-PRODUCTION-WITH-50-CHARACTERS
ALLOWED_HOSTS=piata.ro,www.piata.ro,your-domain.com

# Database Configuration (Production)
DATABASE_URL=postgresql://username:password@localhost:5432/piata_ro

# Email Configuration (Production)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Stripe Configuration (Production)
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_SECRET_KEY
STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_PUBLISHABLE_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_WEBHOOK_SECRET

# Security
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Static Files (Production)
STATIC_ROOT=/var/www/piata.ro/static/
MEDIA_ROOT=/var/www/piata.ro/media/