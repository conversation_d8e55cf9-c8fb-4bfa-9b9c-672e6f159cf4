from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from .models import Listing, UserProfile, Message, Category, ListingReport


class ListingForm(forms.ModelForm):
    """
    Form for creating and editing listings
    """
    images = forms.FileField(
        widget=forms.ClearableFileInput(attrs={'multiple': True, 'accept': 'image/*'}),
        required=False,
        help_text='Selectează până la 10 imagini (PNG, JPG, JPEG, max 5MB fiecare)'
    )

    class Meta:
        model = Listing
        fields = ['title', 'description', 'price', 'category', 'location', 'condition', 'contact_phone']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Titlul anunțului'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 5, 'placeholder': 'Descrierea detaliată'}),
            'price': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Preț în RON'}),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'location': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Orașul sau județul'}),
            'condition': forms.Select(attrs={'class': 'form-control'}),
            'contact_phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Numărul de telefon'}),
        }


class UserProfileForm(forms.ModelForm):
    """
    Form for editing user profile
    """
    class Meta:
        model = UserProfile
        fields = ['phone', 'bio', 'location']
        widgets = {
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'bio': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'location': forms.TextInput(attrs={'class': 'form-control'}),
        }


class MessageForm(forms.ModelForm):
    """
    Form for sending messages
    """
    class Meta:
        model = Message
        fields = ['content']
        widgets = {
            'content': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Scrie mesajul tău...'}),
        }


class UserUpdateForm(forms.ModelForm):
    """
    Form for updating basic user information
    """
    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name']
        widgets = {
            'username': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Nume utilizator'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Email'}),
            'first_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Prenume'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Nume de familie'}),
        }

class ReportForm(forms.ModelForm):
    """
    Form for reporting inappropriate listings
    """
    class Meta:
        model = ListingReport
        fields = ['reason', 'comment']
        widgets = {
            'reason': forms.Select(attrs={'class': 'form-control'}),
            'comment': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Detalii suplimentare (opțional)'}),
        }

class ContactForm(forms.Form):
    """
    Contact form for general inquiries
    """
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Numele tău'})
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Adresa de email'})
    )
    subject = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Subiectul mesajului'})
    )
    message = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 5, 'placeholder': 'Mesajul tău'})
    )
