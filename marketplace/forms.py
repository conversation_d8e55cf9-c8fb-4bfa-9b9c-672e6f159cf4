from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from .models import Listing, UserProfile, Message, Category, ListingReport


class ListingForm(forms.ModelForm):
    """
    Form for creating and editing listings
    """
    class Meta:
        model = Listing
        fields = ['title', 'description', 'price', 'category', 'location', 'condition', 'contact_phone']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Titlul anunțului'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 5, 'placeholder': 'Descrierea detaliată'}),
            'price': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Preț în RON'}),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'location': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Orașul sau județul'}),
            'condition': forms.Select(attrs={'class': 'form-control'}),
            'contact_phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Numărul de telefon'}),
        }


class UserUpdateForm(forms.ModelForm):
    """
    Form for updating basic user information
    """
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Prenume'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Nume'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Adresa de email'}),
        }


class UserProfileForm(forms.ModelForm):
    """
    Form for editing user profile
    """
    class Meta:
        model = UserProfile
        fields = ['phone', 'bio', 'location']
        widgets = {
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'bio': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'location': forms.TextInput(attrs={'class': 'form-control'}),
        }


class MessageForm(forms.ModelForm):
    """
    Form for sending messages
    """
    class Meta:
        model = Message
        fields = ['content']
        widgets = {
            'content': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Scrie mesajul tău...'}),
        }


class ContactForm(forms.Form):
    """
    Contact form for general inquiries
    """
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Numele tău'})
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Adresa de email'})
    )
    subject = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Subiectul mesajului'})
    )
    message = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 5, 'placeholder': 'Mesajul tău'})
    )


class ReportForm(forms.ModelForm):
    """
    Form for reporting listings
    """
    class Meta:
        model = ListingReport
        fields = ['reason', 'comment']
        widgets = {
            'reason': forms.Select(attrs={'class': 'form-control'}),
            'comment': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Adaugă detalii suplimentare (opțional)'}),
        }


class PromoteListingForm(forms.Form):
    """
    Form for promoting a listing
    """
    DURATION_CHOICES = [
        (7, '7 Zile'),
        (14, '14 Zile'),
        (30, '30 Zile'),
    ]
    duration_days = forms.ChoiceField(
        choices=DURATION_CHOICES,
        label="Alege durata promovării",
        widget=forms.Select(attrs={'class': 'form-control mb-3'})
    )
