from django.shortcuts import render, redirect
from django.contrib.auth import login
from django.contrib import messages
from django.conf import settings
import pyotp

from ..models import UserProfile

def verify_mfa(request):
    """
    Verify MFA token for users with MFA enabled
    """
    user_id = request.session.get('mfa_user_id')
    
    if not user_id:
        return redirect('account_login')
    
    if request.method == 'POST':
        token = request.POST.get('token')
        
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            user = User.objects.get(id=user_id)
            profile = UserProfile.objects.get(user=user)
            
            # Verify token
            totp = pyotp.TOTP(profile.mfa_secret)
            if totp.verify(token):
                # Token is valid, log in the user
                login(request, user)
                # Clean up session
                del request.session['mfa_user_id']
                return redirect('marketplace:home')
            else:
                messages.error(request, 'Invalid MFA token')
        except (User.DoesNotExist, UserProfile.DoesNotExist):
            messages.error(request, 'User not found')
            return redirect('account_login')
    
    return render(request, 'mfa_verify.html')


