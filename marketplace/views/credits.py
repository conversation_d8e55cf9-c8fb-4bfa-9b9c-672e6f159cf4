from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json

@login_required
def credits_view(request):
    """View for managing user credits"""
    user_profile = getattr(request.user, 'userprofile', None)
    
    context = {
        'user_profile': user_profile,
        'credits_balance': user_profile.credits if user_profile else 0,
    }
    
    return render(request, 'marketplace/credits.html', context)

@login_required
def credits_dashboard(request):
    """Credits dashboard view - main credits management page"""
    user_profile = getattr(request.user, 'userprofile', None)
    
    context = {
        'user_profile': user_profile,
        'credits_balance': user_profile.credits if user_profile else 0,
        'credit_packages': [
            {'name': 'Basic', 'credits': 100, 'price': 9.99},
            {'name': 'Pro', 'credits': 250, 'price': 19.99},
            {'name': 'Business', 'credits': 500, 'price': 39.99},
        ]
    }
    
    return render(request, 'marketplace/credits_dashboard.html', context)

@login_required
def process_payment_view(request):
    """Handle payment processing for credits"""
    if request.method == 'POST':
        # This would integrate with Stripe payment processing
        messages.success(request, 'Payment processing functionality coming soon!')
        return redirect('marketplace:payment_success')
    
    return render(request, 'marketplace/process_payment.html')

@login_required
def payment_success(request):
    """Payment success page"""
    return render(request, 'marketplace/payment_success.html')

@login_required
def promote_listing_view(request, listing_id):
    """Promote a listing using credits"""
    # This would handle listing promotion logic
    messages.info(request, f'Listing promotion for ID {listing_id} coming soon!')
    return redirect('marketplace:listing_detail', slug='temp')

@login_required
def purchase_credits(request):
    """Handle credit purchases"""
    if request.method == 'POST':
        # This would integrate with payment processing
        messages.success(request, 'Credit purchase functionality coming soon!')
        return redirect('marketplace:credits')
    
    return render(request, 'marketplace/purchase_credits.html')

@login_required
@csrf_exempt
def credits_api(request):
    """API endpoint for credits information"""
    if request.method == 'GET':
        user_profile = getattr(request.user, 'userprofile', None)
        return JsonResponse({
            'credits': user_profile.credits if user_profile else 0,
            'premium': user_profile.is_premium if user_profile else False
        })
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)

@csrf_exempt
def stripe_webhook(request):
    """Handle Stripe webhook events"""
    if request.method == 'POST':
        # This would handle Stripe webhook processing
        return JsonResponse({'status': 'success'})
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)
