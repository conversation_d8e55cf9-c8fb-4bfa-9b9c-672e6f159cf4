from allauth.account.adapter import De<PERSON>ult<PERSON>ccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from django.shortcuts import redirect
from django.urls import reverse
from .models import UserProfile


class MFAAccountAdapter(DefaultAccountAdapter):
    """
    Custom Allauth adapter to integrate MFA into the authentication flow
    """
    
    def login(self, request, user):
        """
        Override login to check for M<PERSON> before completing authentication
        """
        try:
            profile = UserProfile.objects.get(user=user)
            if profile.mfa_enabled and profile.mfa_secret:
                # Store user ID in session for MFA verification
                request.session['mfa_user_id'] = user.id
                # Don't call super().login() yet - we'll do it after MFA
                return redirect('marketplace:verify_mfa')
        except UserProfile.DoesNotExist:
            # No profile exists, create one
            UserProfile.objects.create(user=user)
        
        # No MFA required, proceed with normal login
        return super().login(request, user)
    
    def get_login_redirect_url(self, request):
        """
        Custom redirect after successful login
        """
        # Check if there's a 'next' parameter
        next_url = request.GET.get('next')
        if next_url:
            return next_url
        return super().get_login_redirect_url(request)


class MFASocialAccountAdapter(DefaultSocialAccountAdapter):
    """
    Custom social account adapter to ensure UserProfile creation
    """
    
    def save_user(self, request, sociallogin, form=None):
        """
        Saves a newly signed up social login user
        """
        user = super().save_user(request, sociallogin, form)
        
        # Create UserProfile if it doesn't exist
        if not hasattr(user, 'userprofile'):
            UserProfile.objects.create(user=user)
            
        return user
