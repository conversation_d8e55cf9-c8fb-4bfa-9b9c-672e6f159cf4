from django.db import models
from django.contrib.auth.models import User
from .models import Listing
from decimal import Decimal
import json

class ShippingProvider(models.Model):
    """Shipping provider configuration"""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    api_endpoint = models.URLField()
    is_active = models.BooleanField(default=True)
    supported_services = models.JSONField(default=list)
    coverage_areas = models.JSONField(default=list)
    
    def __str__(self):
        return self.name

class Shipment(models.Model):
    """Shipment tracking and management"""
    
    STATUS_CHOICES = [
        ('created', 'Created'),
        ('picked_up', 'Picked Up'),
        ('in_transit', 'In Transit'),
        ('out_for_delivery', 'Out for Delivery'),
        ('delivered', 'Delivered'),
        ('failed_delivery', 'Failed Delivery'),
        ('returned', 'Returned'),
        ('cancelled', 'Cancelled'),
    ]
    
    SERVICE_CHOICES = [
        ('standard', 'Standard'),
        ('express', 'Express'),
        ('same_day', 'Same Day'),
        ('economy', 'Economy'),
    ]
    
    # Core shipment info
    listing = models.ForeignKey(Listing, on_delete=models.CASCADE, related_name='shipments')
    seller = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_shipments')
    buyer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_shipments')
    provider = models.ForeignKey(ShippingProvider, on_delete=models.CASCADE)
    
    # Tracking information
    awb_number = models.CharField(max_length=50, unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='created')
    service_type = models.CharField(max_length=20, choices=SERVICE_CHOICES, default='standard')
    
    # Addresses
    sender_data = models.JSONField()
    recipient_data = models.JSONField()
    
    # Package details
    weight = models.DecimalField(max_digits=8, decimal_places=2)
    length = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    width = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    height = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    declared_value = models.DecimalField(max_digits=10, decimal_places=2)
    contents = models.TextField()
    fragile = models.BooleanField(default=False)
    
    # Costs and payment
    shipping_cost = models.DecimalField(max_digits=10, decimal_places=2)
    insurance_cost = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    cod_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_cost = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Delivery information
    estimated_delivery = models.DateTimeField(null=True, blank=True)
    actual_delivery = models.DateTimeField(null=True, blank=True)
    delivery_attempts = models.IntegerField(default=0)
    
    # Tracking and notifications
    tracking_url = models.URLField(blank=True)
    last_location = models.CharField(max_length=200, blank=True)
    delivery_notes = models.TextField(blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Shipment {self.awb_number} - {self.listing.title}"
    
    @property
    def is_delivered(self):
        return self.status == 'delivered'
    
    @property
    def can_be_cancelled(self):
        return self.status in ['created', 'picked_up']

class ShippingEvent(models.Model):
    """Shipment tracking events"""
    shipment = models.ForeignKey(Shipment, on_delete=models.CASCADE, related_name='events')
    timestamp = models.DateTimeField()
    status = models.CharField(max_length=50)
    location = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    courier_name = models.CharField(max_length=100, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.shipment.awb_number} - {self.status} at {self.timestamp}"

class PickupRequest(models.Model):
    """Package pickup scheduling"""
    
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('confirmed', 'Confirmed'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    shipment = models.OneToOneField(Shipment, on_delete=models.CASCADE, related_name='pickup_request')
    pickup_date = models.DateField()
    time_interval = models.CharField(max_length=20, default='09:00-17:00')
    
    # Pickup address
    contact_name = models.CharField(max_length=100)
    contact_phone = models.CharField(max_length=20)
    address = models.TextField()
    locality = models.CharField(max_length=100)
    county = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=10, blank=True)
    
    # Status and tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    confirmation_code = models.CharField(max_length=20, blank=True)
    pickup_notes = models.TextField(blank=True)
    packages_count = models.IntegerField(default=1)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Pickup {self.confirmation_code} - {self.pickup_date}"

class DeliveryProof(models.Model):
    """Proof of delivery documentation"""
    shipment = models.OneToOneField(Shipment, on_delete=models.CASCADE, related_name='delivery_proof')
    
    # Delivery details
    delivered_at = models.DateTimeField()
    recipient_name = models.CharField(max_length=100)
    delivery_location = models.CharField(max_length=200)
    courier_name = models.CharField(max_length=100)
    
    # Proof documentation
    signature_image = models.ImageField(upload_to='delivery_signatures/', null=True, blank=True)
    photo_proof = models.ImageField(upload_to='delivery_photos/', null=True, blank=True)
    delivery_notes = models.TextField(blank=True)
    
    # Verification
    signature_available = models.BooleanField(default=False)
    photo_available = models.BooleanField(default=False)
    verified = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Delivery proof for {self.shipment.awb_number}"

class ShippingRate(models.Model):
    """Shipping rate calculations and caching"""
    provider = models.ForeignKey(ShippingProvider, on_delete=models.CASCADE)
    origin_locality = models.CharField(max_length=100)
    destination_locality = models.CharField(max_length=100)
    service_type = models.CharField(max_length=20)
    
    # Rate details
    base_rate = models.DecimalField(max_digits=10, decimal_places=2)
    weight_rate = models.DecimalField(max_digits=10, decimal_places=2)
    distance_factor = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('1.00'))
    
    # Validity
    valid_from = models.DateTimeField()
    valid_until = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['provider', 'origin_locality', 'destination_locality', 'service_type']
    
    def __str__(self):
        return f"{self.provider.name} - {self.origin_locality} to {self.destination_locality}"

class ShippingNotification(models.Model):
    """Shipping notifications and alerts"""
    
    TYPE_CHOICES = [
        ('shipment_created', 'Shipment Created'),
        ('pickup_scheduled', 'Pickup Scheduled'),
        ('in_transit', 'In Transit'),
        ('out_for_delivery', 'Out for Delivery'),
        ('delivered', 'Delivered'),
        ('delivery_failed', 'Delivery Failed'),
        ('exception', 'Exception'),
    ]
    
    CHANNEL_CHOICES = [
        ('email', 'Email'),
        ('sms', 'SMS'),
        ('push', 'Push Notification'),
        ('in_app', 'In-App'),
    ]
    
    shipment = models.ForeignKey(Shipment, on_delete=models.CASCADE, related_name='notifications')
    recipient = models.ForeignKey(User, on_delete=models.CASCADE)
    
    notification_type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    channel = models.CharField(max_length=10, choices=CHANNEL_CHOICES)
    
    title = models.CharField(max_length=200)
    message = models.TextField()
    
    # Delivery status
    sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered = models.BooleanField(default=False)
    read = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.notification_type} - {self.recipient.username}"
