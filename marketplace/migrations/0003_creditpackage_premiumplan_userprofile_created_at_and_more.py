# Generated by Django 4.2.22 on 2025-06-07 04:38

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('marketplace', '0002_remove_listing_images_alter_listing_currency_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CreditPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('credits', models.DecimalField(decimal_places=1, max_digits=10)),
                ('price_eur', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price_ron', models.DecimalField(decimal_places=2, max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('description', models.TextField(blank=True)),
                ('stripe_price_id', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['credits'],
            },
        ),
        migrations.CreateModel(
            name='PremiumPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('plan_type', models.CharField(choices=[('monthly', 'Monthly'), ('yearly', 'Yearly'), ('lifetime', 'Lifetime')], max_length=20)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='RON', max_length=3)),
                ('credits_included', models.PositiveIntegerField(default=0)),
                ('max_premium_listings', models.PositiveIntegerField(default=5)),
                ('max_featured_listings', models.PositiveIntegerField(default=2)),
                ('priority_support', models.BooleanField(default=True)),
                ('analytics_access', models.BooleanField(default=True)),
                ('stripe_price_id', models.CharField(blank=True, max_length=100, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('description', models.TextField(blank=True)),
                ('features', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['price'],
            },
        ),
        migrations.AddField(
            model_name='userprofile',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='credits_balance',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='total_credits_purchased',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.CreateModel(
            name='UserAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_views', models.PositiveIntegerField(default=0)),
                ('total_messages', models.PositiveIntegerField(default=0)),
                ('total_favorites', models.PositiveIntegerField(default=0)),
                ('total_listings', models.PositiveIntegerField(default=0)),
                ('total_sales', models.PositiveIntegerField(default=0)),
                ('conversion_rate', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('monthly_data', models.JSONField(default=dict)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('payment_type', models.CharField(choices=[('credits', 'Credits Purchase'), ('premium', 'Premium Subscription'), ('listing_boost', 'Listing Boost')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='RON', max_length=3)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('canceled', 'Canceled'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('stripe_payment_intent_id', models.CharField(max_length=100, unique=True)),
                ('stripe_customer_id', models.CharField(blank=True, max_length=100, null=True)),
                ('metadata', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ListingBoost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('boost_type', models.CharField(choices=[('featured', 'Featured Listing'), ('top_ad', 'Top Ad'), ('highlighted', 'Highlighted'), ('urgent', 'Urgent')], max_length=20)),
                ('credits_cost', models.PositiveIntegerField()),
                ('duration_days', models.PositiveIntegerField()),
                ('starts_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('expires_at', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('listing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='boosts', to='marketplace.listing')),
                ('payment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='marketplace.payment')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CreditTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('purchase', 'Purchase'), ('spent', 'Spent'), ('bonus', 'Bonus'), ('refund', 'Refund')], max_length=20)),
                ('amount', models.IntegerField()),
                ('description', models.CharField(max_length=255)),
                ('payment_intent_id', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('listing', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='marketplace.listing')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
