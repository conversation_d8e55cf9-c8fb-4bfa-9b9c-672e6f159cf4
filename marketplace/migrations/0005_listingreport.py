# Generated by Django 4.2.22 on 2025-06-07 06:20

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('marketplace', '0004_add_location_services'),
    ]

    operations = [
        migrations.CreateModel(
            name='ListingReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.CharField(choices=[('spam', 'Spam sau anunț duplicat'), ('inappropriate', 'Conținut nepotrivit'), ('fake', 'Anunț fals'), ('price', 'Preț incorect sau înșelător'), ('contact', 'Informații de contact false'), ('copyright', 'Încălcarea drepturilor de autor'), ('other', 'Altul')], max_length=20)),
                ('comment', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'În așteptare'), ('reviewed', 'Revizuit'), ('resolved', 'Rezolvat'), ('dismissed', 'Respins')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, help_text='Admin notes', null=True)),
                ('listing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='marketplace.listing')),
                ('reporter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submitted_reports', to=settings.AUTH_USER_MODEL)),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_reports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('listing', 'reporter')},
            },
        ),
    ]
