# Generated by Django 4.2.22 on 2025-06-07 06:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0003_creditpackage_premiumplan_userprofile_created_at_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='listing',
            name='address',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='listing',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='listing',
            name='country',
            field=models.CharField(default='România', max_length=100),
        ),
        migrations.AddField(
            model_name='listing',
            name='county',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='listing',
            name='latitude',
            field=models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='listing',
            name='location_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='listing',
            name='longitude',
            field=models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='listing',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('latitude', models.DecimalField(decimal_places=7, max_digits=10)),
                ('longitude', models.DecimalField(decimal_places=7, max_digits=10)),
                ('address', models.CharField(blank=True, max_length=255, null=True)),
                ('city', models.CharField(max_length=100)),
                ('county', models.CharField(max_length=100)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('country', models.CharField(default='România', max_length=100)),
                ('location_type', models.CharField(choices=[('city', 'City'), ('neighborhood', 'Neighborhood'), ('landmark', 'Landmark'), ('custom', 'Custom Location')], default='city', max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'indexes': [models.Index(fields=['city', 'county'], name='marketplace_city_d5a0b8_idx'), models.Index(fields=['latitude', 'longitude'], name='marketplace_latitud_1f45c2_idx')],
                'unique_together': {('latitude', 'longitude')},
            },
        ),
    ]
