# Generated by Django 5.2.4 on 2025-07-24 18:27

from django.db import migrations, models
from django.utils.text import slugify


def populate_listing_slugs(apps, schema_editor):
    """Populate slug field for existing listings"""
    Listing = apps.get_model('marketplace', 'Listing')

    for listing in Listing.objects.all():
        if not listing.slug:
            # Generate base slug from title
            base_slug = slugify(listing.title) if listing.title else 'anunt'
            unique_slug = base_slug
            num = 1

            # Ensure uniqueness
            while Listing.objects.filter(slug=unique_slug).exists():
                unique_slug = f'{base_slug}-{num}'
                num += 1

            listing.slug = unique_slug
            listing.save(update_fields=['slug'])


def reverse_populate_listing_slugs(apps, schema_editor):
    """Reverse operation - clear slug field"""
    Listing = apps.get_model('marketplace', 'Listing')
    Listing.objects.all().update(slug='')


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0014_listingimage_thumbnail_delete_report'),
    ]

    operations = [
        migrations.AddField(
            model_name='listing',
            name='condition',
            field=models.CharField(choices=[('new', 'Nou'), ('used', 'Utilizat'), ('for_parts', 'Pentru piese')], default='used', max_length=20, verbose_name='Condiție'),
        ),
        migrations.AddField(
            model_name='listing',
            name='contact_phone',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Telefon de contact'),
        ),
        # First add the slug field without unique constraint
        migrations.AddField(
            model_name='listing',
            name='slug',
            field=models.SlugField(blank=True, help_text='URL-friendly version of the title, auto-generated.', max_length=255),
        ),
        # Populate slugs for existing records
        migrations.RunPython(populate_listing_slugs, reverse_populate_listing_slugs),
        # Now add the unique constraint
        migrations.AlterField(
            model_name='listing',
            name='slug',
            field=models.SlugField(blank=True, help_text='URL-friendly version of the title, auto-generated.', max_length=255, unique=True),
        ),
    ]
