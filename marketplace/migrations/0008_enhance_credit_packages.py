# Generated by Django 4.2.23 on 2025-07-05 08:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0007_add_auto_repost_fields'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='creditpackage',
            options={'ordering': ['base_credits'], 'verbose_name': 'Credit Package', 'verbose_name_plural': 'Credit Packages'},
        ),
        migrations.AlterModelOptions(
            name='listingimage',
            options={'ordering': ['order', 'created_at'], 'verbose_name': 'Listing Image', 'verbose_name_plural': 'Listing Images'},
        ),
        migrations.RenameField(
            model_name='creditpackage',
            old_name='credits',
            new_name='base_credits',
        ),
        migrations.AddField(
            model_name='creditpackage',
            name='bonus_credits',
            field=models.DecimalField(decimal_places=1, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='creditpackage',
            name='expires_days',
            field=models.PositiveIntegerField(default=365, help_text='Days before purchased credits expire'),
        ),
        migrations.AddField(
            model_name='creditpackage',
            name='modified_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='creditpackage',
            name='referral_bonus',
            field=models.DecimalField(decimal_places=1, default=0, help_text='Bonus credits for both referrer and referee', max_digits=10),
        ),
        migrations.AddField(
            model_name='creditpackage',
            name='tier',
            field=models.CharField(choices=[('basic', 'Basic'), ('standard', 'Standard'), ('premium', 'Premium')], default='basic', max_length=20),
        ),
        migrations.AddField(
            model_name='listingimage',
            name='thumbnail',
            field=models.ImageField(blank=True, upload_to='thumbnails/'),
        ),
        migrations.AlterField(
            model_name='listingimage',
            name='image',
            field=models.ImageField(upload_to='listings/'),
        ),
    ]
