# Generated by Django 5.2.4 on 2025-07-07 08:59

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("marketplace", "0010_report_alter_listingreport_listing_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="userprofile",
            name="clerk_user_id",
            field=models.CharField(
                blank=True,
                help_text="Clerk User ID",
                max_length=255,
                null=True,
                unique=True,
            ),
        ),
        migrations.CreateModel(
            name="EscrowPayment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("created", "Created"),
                            ("funded", "Funded"),
                            ("released", "Released"),
                            ("refunded", "Refunded"),
                            ("disputed", "Disputed"),
                            ("resolved", "Resolved"),
                        ],
                        default="created",
                        max_length=10,
                    ),
                ),
                ("payment_intent_id", models.CharField(max_length=100)),
                (
                    "dispute_reason",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("item_not_received", "Item Not Received"),
                            ("item_not_as_described", "Item Not As Described"),
                            ("unauthorized_transaction", "Unauthorized Transaction"),
                            ("other", "Other"),
                        ],
                        max_length=25,
                        null=True,
                    ),
                ),
                ("dispute_description", models.TextField(blank=True, null=True)),
                (
                    "dispute_evidence",
                    models.FileField(blank=True, null=True, upload_to="disputes/"),
                ),
                ("dispute_resolution", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "buyer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="escrow_buys",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "listing",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="marketplace.listing",
                    ),
                ),
                (
                    "seller",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="escrow_sales",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Escrow Payment",
                "verbose_name_plural": "Escrow Payments",
            },
        ),
        migrations.CreateModel(
            name="IdentityVerification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("expired", "Expired"),
                        ],
                        default="pending",
                        max_length=10,
                    ),
                ),
                ("verification_date", models.DateTimeField(blank=True, null=True)),
                ("expiration_date", models.DateTimeField(blank=True, null=True)),
                ("scan_reference", models.CharField(blank=True, max_length=100)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="identity_verification",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Identity Verification",
                "verbose_name_plural": "Identity Verifications",
            },
        ),
    ]
