# Generated by Django 4.2.23 on 2025-07-05 10:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('marketplace', '0009_add_mfa_security_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.CharField(choices=[('spam', 'Spam or misleading'), ('fraud', 'Fraud or scam'), ('prohibited', 'Prohibited item'), ('wrong_category', 'Wrong category'), ('duplicate', 'Duplicate listing'), ('other', 'Other reason')], max_length=20)),
                ('description', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_resolved', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Listing Report',
                'verbose_name_plural': 'Listing Reports',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AlterField(
            model_name='listingreport',
            name='listing',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='listing_reports', to='marketplace.listing'),
        ),
        migrations.AddIndex(
            model_name='listing',
            index=models.Index(fields=['is_featured', 'created_at'], name='marketplace_is_feat_292243_idx'),
        ),
        migrations.AddIndex(
            model_name='listing',
            index=models.Index(fields=['price', 'status'], name='marketplace_price_ad1561_idx'),
        ),
        migrations.AddIndex(
            model_name='listing',
            index=models.Index(fields=['city', 'status'], name='marketplace_city_2ea38b_idx'),
        ),
        migrations.AddIndex(
            model_name='listing',
            index=models.Index(fields=['latitude', 'longitude'], name='marketplace_latitud_b0088f_idx'),
        ),
        migrations.AddField(
            model_name='report',
            name='listing',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='marketplace.listing'),
        ),
        migrations.AddField(
            model_name='report',
            name='reporter',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports_made', to=settings.AUTH_USER_MODEL),
        ),
    ]
