{% extends 'marketplace/base.html' %}
{% load i18n %}

{% block title %}Înregistrare - Piața.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-12">
    <div class="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden">
        <!-- Header -->
        <div class="bg-accent text-white p-6 text-center">
            <i class="fas fa-user-plus text-4xl mb-3"></i>
            <h2 class="text-2xl font-bold">Creează cont nou</h2>
            <p class="text-sm opacity-80">Alătură-te comunității Piața.ro</p>
        </div>
        
        <!-- Form -->
        <div class="p-6">
            <form class="signup" id="signup_form" method="post" action="{% url 'account_signup' %}">{%load socialaccount%}
                {% csrf_token %}
                
                {% if form.errors %}
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                    <p class="font-medium">{% trans "Te rugăm să corectezi următoarele erori:" %}</p>
                    {% for field in form %}
                        {% if field.errors %}
                        <p class="text-sm">{{ field.label }}: {{ field.errors|striptags }}</p>
                        {% endif %}
                    {% endfor %}
                    
                    {% if form.non_field_errors %}
                    <p class="text-sm">{{ form.non_field_errors|striptags }}</p>
                    {% endif %}
                </div>
                {% endif %}
                
                {% for field in form %}
                <div class="mb-4">
                    <label for="{{ field.id_for_label }}" class="block text-gray-700 text-sm font-medium mb-2">{{ field.label }}:</label>
                    <input type="{{ field.field.widget.input_type }}"
                           name="{{ field.name }}"
                           id="{{ field.id_for_label }}"
                           {% if field.field.required %}required{% endif %}
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent"
                           {% if field.name == 'email' %}placeholder="Adresa ta de email" autocomplete="email"{% endif %}
                           {% if field.name == 'password1' %}placeholder="Creează o parolă sigură" autocomplete="new-password"{% endif %}
                           {% if field.name == 'password2' %}placeholder="Confirmă parola" autocomplete="new-password"{% endif %}>
                    {% if field.errors %}
                        <div class="text-red-600 text-sm mt-1">{{ field.errors|striptags }}</div>
                    {% endif %}
                </div>
                {% endfor %}
                
                <div class="flex items-start mb-4">
                    <div class="flex items-center h-5">
                        <input type="checkbox" name="terms" id="id_terms" required
                               class="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="id_terms" class="text-gray-700">
                            Am citit și sunt de acord cu <a href="{% url 'marketplace:terms' %}" target="_blank" class="text-accent hover:underline">Termenii și Condițiile</a> și <a href="{% url 'marketplace:privacy' %}" target="_blank" class="text-accent hover:underline">Politica de Confidențialitate</a>
                        </label>
                    </div>
                </div>
                
                <div class="mb-4">
                    <button type="submit" class="w-full bg-accent text-white py-2 px-4 rounded-md hover:bg-green-600 transition duration-200">
                        <i class="fas fa-user-check mr-2"></i> Creează cont
                    </button>
                </div>
            </form>
            
            <div class="relative flex py-4 items-center">
                <div class="flex-grow border-t border-gray-300"></div>
                <span class="flex-shrink mx-4 text-gray-400 text-sm">SAU</span>
                <div class="flex-grow border-t border-gray-300"></div>
            </div>

            {% get_providers as socialaccount_providers %}

            {% if socialaccount_providers %}
            <div class="socialaccount_ballot">
                <ul class="socialaccount_providers">
                    {% for provider in socialaccount_providers %}
                    {% if provider.id == 'google' %}
                    <li>
                        <a title="{{provider.name}}" class="w-full flex items-center justify-center bg-white border border-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-50 transition duration-200"
                           href="{% provider_login_url provider.id process='login' scope=scope auth_params=auth_params %}">
                            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="h-5 w-5 mr-3">
                            Continuă cu Google
                        </a>
                    </li>
                    {% endif %}
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
            
            <p class="text-center text-gray-600 text-sm">
                Ai deja un cont?
                <a href="{% url 'account_login' %}" class="text-accent hover:text-green-600 font-medium">
                    Autentifică-te
                </a>
            </p>
        </div>
    </div>
</div>
{% endblock %}
