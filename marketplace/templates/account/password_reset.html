{% extends 'marketplace/base.html' %}
{% load i18n %}

{% block title %}Resetare parolă - Piața.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-12">
    <div class="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden">
        <!-- Header -->
        <div class="bg-gray-700 text-white p-6 text-center">
            <i class="fas fa-key text-4xl mb-3"></i>
            <h2 class="text-2xl font-bold">Ai uitat parola?</h2>
            <p class="text-sm opacity-80">Introdu adresa de email pentru a o reseta</p>
        </div>
        
        <!-- Form -->
        <div class="p-6">
            <form method="POST" action="{% url 'account_reset_password' %}" class="password_reset">
                {% csrf_token %}
                
                {% if form.errors %}
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                    <p class="font-medium">{% trans "Te rugăm să corectezi următoarele erori:" %}</p>
                    {% for field in form %}
                        {% if field.errors %}
                        <p class="text-sm">{{ field.label }}: {{ field.errors|striptags }}</p>
                        {% endif %}
                    {% endfor %}
                    
                    {% if form.non_field_errors %}
                    <p class="text-sm">{{ form.non_field_errors|striptags }}</p>
                    {% endif %}
                </div>
                {% endif %}
                
                <div class="mb-4">
                    <label for="id_email" class="block text-gray-700 text-sm font-medium mb-2">Email:</label>
                    <input type="email" name="email" placeholder="Adresa ta de email" autocomplete="email" required id="id_email" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500">
                </div>
                
                <div class="mb-4">
                    <button type="submit" class="w-full bg-gray-700 text-white py-2 px-4 rounded-md hover:bg-gray-800 transition duration-200">
                        <i class="fas fa-paper-plane mr-2"></i> Trimite link de resetare
                    </button>
                </div>
                
                <p class="text-center text-gray-600 text-sm">
                    <a href="{% url 'account_login' %}" class="text-gray-700 hover:text-gray-900 font-medium">
                        <i class="fas fa-arrow-left mr-1"></i> Înapoi la autentificare
                    </a>
                </p>
            </form>
        </div>
    </div>
</div>
{% endblock %}
