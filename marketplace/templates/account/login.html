{% extends 'marketplace/base.html' %}
{% load i18n %}

{% block title %}Autentificare - Piața.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-12">
    <div class="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden">
        <!-- Header -->
        <div class="bg-primary text-white p-6 text-center">
            <i class="fas fa-lock text-4xl mb-3"></i>
            <h2 class="text-2xl font-bold">Autentificare</h2>
            <p class="text-sm opacity-80">Intră în contul tău Piața.ro</p>
        </div>
        
        <!-- Form -->
        <div class="p-6">
            <form class="login" method="POST" action="{% url 'account_login' %}">{%load socialaccount%}
                {% csrf_token %}
                
                {% if form.errors %}
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                    <p class="font-medium">{% trans "Te rugăm să corectezi următoarele erori:" %}</p>
                    {% for field in form %}
                        {% if field.errors %}
                        <p class="text-sm">{{ field.label }}: {{ field.errors|striptags }}</p>
                        {% endif %}
                    {% endfor %}
                    
                    {% if form.non_field_errors %}
                    <p class="text-sm">{{ form.non_field_errors|striptags }}</p>
                    {% endif %}
                </div>
                {% endif %}
                
                <div class="mb-4">
                    <label for="id_username" class="block text-gray-700 text-sm font-medium mb-2">Email:</label>
                    <input type="email" name="username" placeholder="Adresa ta de email" autocomplete="email" required id="id_username"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                </div>
                
                <div class="mb-4">
                    <label for="id_password" class="block text-gray-700 text-sm font-medium mb-2">Parolă:</label>
                    <input type="password" name="password" placeholder="Parola ta" autocomplete="current-password" required id="id_password" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                </div>
                
                <div class="flex items-center mb-4">
                    <input type="checkbox" name="remember" id="id_remember" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    <label for="id_remember" class="ml-2 block text-sm text-gray-700">Ține-mă minte</label>
                </div>
                
                <div class="mb-4">
                    <button type="submit" class="w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-primary-dark transition duration-200">
                        <i class="fas fa-sign-in-alt mr-2"></i> Autentificare
                    </button>
                </div>
                
                <div class="text-center mb-4">
                    <a href="{% url 'account_reset_password' %}" class="text-primary hover:text-primary-dark text-sm font-medium">
                        {% trans "Ai uitat parola?" %}
                    </a>
                </div>
            </form>
            
            
            <p class="text-center text-gray-600 text-sm">
                Nu ai cont?
                <a href="{% url 'account_signup' %}" class="text-primary hover:text-primary-dark font-medium">
                    Creează un cont nou
                </a>
            </p>
        </div>
    </div>
</div>
{% endblock %}
