{% extends 'marketplace/base.html' %}

{% block title %}Autentificare - Piata.ro{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary">
                <i class="fas fa-user text-white text-xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Intră în cont
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Sau 
                <a href="{% url 'register' %}" class="font-medium text-primary hover:text-primary-dark">
                    creează un cont nou
                </a>
            </p>
        </div>
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            <input type="hidden" name="remember" value="true">
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="id_username" class="sr-only">Nume utilizator</label>
                    <input id="id_username" name="username" type="text" required 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm" 
                           placeholder="Nume utilizator">
                </div>
                <div>
                    <label for="id_password" class="sr-only">Parola</label>
                    <input id="id_password" name="password" type="password" required 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm" 
                           placeholder="Parola">
                </div>
            </div>

            {% if form.errors %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Eroare la autentificare
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc list-inside space-y-1">
                                    {% for field, errors in form.errors.items %}
                                        {% for error in errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember-me" name="remember-me" type="checkbox" 
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                        Ține-mă minte
                    </label>
                </div>

                <div class="text-sm">
                    <a href="{% url 'account_reset_password' %}" class="font-medium text-primary hover:text-primary-dark">
                        Ai uitat parola?
                    </a>
                </div>
            </div>

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-lock text-primary-light group-hover:text-primary-lighter"></i>
                    </span>
                    Intră în cont
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
