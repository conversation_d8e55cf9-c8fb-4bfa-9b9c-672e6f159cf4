{% extends "admin/base.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}">
        <span style="color: white; font-weight: 700;">Piața.ro</span> <span style="opacity: 0.8;">Admin</span>
    </a>
</h1>
{% endblock %}

{% block extrastyle %}
{{ block.super }}
<style>
/* Modern Admin Theme */
:root {
    --primary: #0056b3;
    --primary-dark: #004494;
    --secondary: #6c757d;
    --success: #28a745;
    --info: #17a2b8;
    --warning: #ffc107;
    --danger: #dc3545;
    --light: #f8f9fa;
    --dark: #343a40;
    --white: #ffffff;
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;
    --card-border: #e9ecef;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --input-focus-border: #80bdff;
    --input-focus-shadow: rgba(0, 123, 255, 0.25);
}

/* Base Styles */
body {
    background-color: var(--body-bg);
    font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Header */
#header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 15px 40px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

#branding h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

#branding h1 a {
    color: var(--white);
    text-decoration: none;
}

#user-tools {
    font-size: 0.85rem;
}

#user-tools a {
    color: rgba(255, 255, 255, 0.85);
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
}

#user-tools a:hover {
    color: var(--white);
    border-bottom-color: var(--white);
}

/* Content */
#content {
    padding: 20px 40px;
}

.module {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.module:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.module h2, .module caption {
    background: var(--primary);
    color: var(--white);
    padding: 12px 15px;
    font-size: 1rem;
    font-weight: 600;
}

/* Forms */
.form-row {
    padding: 12px 15px;
    border-bottom: 1px solid var(--card-border);
}

.form-row:last-child {
    border-bottom: none;
}

input[type="text"], 
input[type="password"], 
input[type="email"], 
input[type="number"], 
input[type="url"], 
input[type="tel"], 
textarea, 
select, 
.vTextField {
    border: 1px solid var(--input-border);
    border-radius: 4px;
    padding: 8px 12px;
    background-color: var(--input-bg);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input[type="text"]:focus, 
input[type="password"]:focus, 
input[type="email"]:focus, 
input[type="number"]:focus, 
input[type="url"]:focus, 
input[type="tel"]:focus, 
textarea:focus, 
select:focus, 
.vTextField:focus {
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 0.2rem var(--input-focus-shadow);
    outline: 0;
}

/* Buttons */
.button, input[type=submit], input[type=button], .submit-row input {
    background: var(--primary);
    color: var(--white);
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 600;
    transition: background-color 0.2s ease;
}

.button:hover, input[type=submit]:hover, input[type=button]:hover, .submit-row input:hover {
    background: var(--primary-dark);
}

.button.default, input[type=submit].default, .submit-row input.default {
    background: var(--success);
}

.button.default:hover, input[type=submit].default:hover, .submit-row input.default:hover {
    background: #218838;
}

/* Tables */
table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
}

thead th {
    background: var(--light);
    color: var(--dark);
    font-weight: 600;
    padding: 12px 15px;
    border-bottom: 2px solid var(--card-border);
}

tbody tr:nth-child(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

td, th {
    padding: 10px 15px;
    border-bottom: 1px solid var(--card-border);
}

/* Dashboard */
.dashboard .module table th {
    width: 100%;
}

.dashboard .module table td {
    white-space: nowrap;
}

.dashboard .module table td a {
    color: var(--primary);
    text-decoration: none;
}

.dashboard .module table td a:hover {
    text-decoration: underline;
}

/* Breadcrumbs */
div.breadcrumbs {
    background: var(--light);
    color: var(--dark);
    padding: 15px 40px;
    border-bottom: 1px solid var(--card-border);
}

div.breadcrumbs a {
    color: var(--primary);
}

div.breadcrumbs a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Messages */
ul.messagelist li {
    padding: 12px 40px;
    margin: 0;
    border-bottom: 1px solid var(--card-border);
}

ul.messagelist li.success {
    background: #d4edda;
    color: #155724;
}

ul.messagelist li.error {
    background: #f8d7da;
    color: #721c24;
}

ul.messagelist li.warning {
    background: #fff3cd;
    color: #856404;
}

/* Pagination */
.paginator {
    padding: 15px 0;
    font-size: 0.9rem;
}

.paginator a {
    padding: 5px 10px;
    border: 1px solid var(--card-border);
    border-radius: 4px;
    margin: 0 3px;
    color: var(--primary);
    text-decoration: none;
}

.paginator a:hover {
    background: var(--light);
}

.paginator .this-page {
    padding: 5px 10px;
    border: 1px solid var(--primary);
    border-radius: 4px;
    margin: 0 3px;
    background: var(--primary);
    color: var(--white);
    font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    #header {
        padding: 15px 20px;
    }
    
    #content {
        padding: 15px 20px;
    }
    
    div.breadcrumbs {
        padding: 10px 20px;
    }
}
</style>
{% endblock %}

{% block nav-global %}{% endblock %}