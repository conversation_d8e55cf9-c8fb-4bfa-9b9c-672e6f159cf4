{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<style>
    .assistant-container {
        max-width: 1200px;
        margin: 20px auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .assistant-header {
        background: linear-gradient(135deg, #0056b3 0%, #004494 100%);
        color: white;
        padding: 15px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .assistant-header h2 {
        margin: 0;
        font-size: 1.5rem;
    }
    
    .assistant-body {
        display: flex;
        height: calc(100vh - 250px);
        min-height: 500px;
    }
    
    .assistant-sidebar {
        width: 250px;
        background: #f5f7fa;
        border-right: 1px solid #e9ecef;
        overflow-y: auto;
        padding: 15px;
    }
    
    .assistant-main {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .assistant-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #f9f9f9;
    }
    
    .message {
        margin-bottom: 15px;
        max-width: 80%;
    }
    
    .message-user {
        margin-left: auto;
        background: #0056b3;
        color: white;
        border-radius: 18px 18px 0 18px;
        padding: 10px 15px;
    }
    
    .message-assistant {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 18px 18px 18px 0;
        padding: 10px 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }
    
    .assistant-input {
        padding: 15px;
        border-top: 1px solid #e9ecef;
        background: white;
    }
    
    .assistant-form {
        display: flex;
        gap: 10px;
    }
    
    .assistant-textarea {
        flex: 1;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 10px;
        resize: none;
        min-height: 60px;
        font-family: inherit;
    }
    
    .assistant-textarea:focus {
        outline: none;
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .assistant-submit {
        background: #0056b3;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 0 20px;
        cursor: pointer;
        font-weight: 600;
        transition: background 0.2s;
    }
    
    .assistant-submit:hover {
        background: #004494;
    }
    
    .sidebar-title {
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .sidebar-section {
        margin-bottom: 20px;
    }
    
    .sidebar-item {
        padding: 8px 10px;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.2s;
        margin-bottom: 5px;
        font-size: 0.9rem;
    }
    
    .sidebar-item:hover {
        background: #e9ecef;
    }
    
    .sidebar-item.active {
        background: #e2e8f0;
        font-weight: 500;
    }
    
    .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(0, 86, 179, 0.3);
        border-radius: 50%;
        border-top-color: #0056b3;
        animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="assistant-container">
    <div class="assistant-header">
        <h2>
            <i class="fas fa-robot" style="margin-right: 10px;"></i>
            Piața.ro Admin Assistant
        </h2>
        <div>
            <span id="status-indicator" style="font-size: 0.8rem; opacity: 0.8;">Ready</span>
        </div>
    </div>
    
    <div class="assistant-body">
        <div class="assistant-sidebar">
            <div class="sidebar-section">
                <div class="sidebar-title">Sample Questions</div>
                <div class="sidebar-item" onclick="insertQuery('How many active listings do we have?')">
                    Count active listings
                </div>
                <div class="sidebar-item" onclick="insertQuery('Show me the top 5 categories by listing count')">
                    Top categories
                </div>
                <div class="sidebar-item" onclick="insertQuery('How many users registered this month?')">
                    New users this month
                </div>
                <div class="sidebar-item" onclick="insertQuery('What are the most viewed listings?')">
                    Most viewed listings
                </div>
            </div>
            
            <div class="sidebar-section">
                <div class="sidebar-title">Recent Questions</div>
                <div id="recent-queries">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
        
        <div class="assistant-main">
            <div class="assistant-messages" id="assistant-messages">
                <div class="message message-assistant">
                    <p>Welcome to Piața.ro Admin Assistant! I can help you with administrative tasks and answer questions about your marketplace data.</p>
                    <p>Try asking me about listings, users, categories, or any other aspect of your platform.</p>
                </div>
            </div>
            
            <div class="assistant-input">
                <form id="assistant-form" class="assistant-form">
                    <textarea 
                        id="assistant-input" 
                        name="message" 
                        class="assistant-textarea" 
                        placeholder="Ask me anything about your marketplace..."
                        rows="3"
                    ></textarea>
                    <button type="submit" class="assistant-submit">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Store recent queries
    let recentQueries = [];
    
    // Function to add a message to the chat
    function addMessage(content, isUser = false) {
        const messagesContainer = document.getElementById('assistant-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'message-user' : 'message-assistant'}`;
        messageDiv.innerHTML = content;
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Function to insert a query into the input field
    function insertQuery(query) {
        document.getElementById('assistant-input').value = query;
        document.getElementById('assistant-input').focus();
    }
    
    // Function to update recent queries sidebar
    function updateRecentQueries() {
        const container = document.getElementById('recent-queries');
        container.innerHTML = '';
        
        recentQueries.forEach(query => {
            const div = document.createElement('div');
            div.className = 'sidebar-item';
            div.textContent = query.length > 30 ? query.substring(0, 30) + '...' : query;
            div.onclick = () => insertQuery(query);
            container.appendChild(div);
        });
        
        if (recentQueries.length === 0) {
            const div = document.createElement('div');
            div.textContent = 'No recent questions';
            div.style.padding = '8px 10px';
            div.style.color = '#6c757d';
            div.style.fontStyle = 'italic';
            div.style.fontSize = '0.9rem';
            container.appendChild(div);
        }
    }
    
    // Initialize recent queries
    updateRecentQueries();
    
    // Handle form submission
    document.getElementById('assistant-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const message = document.getElementById('assistant-input').value.trim();
        
        if (!message) return;
        
        // Add user message
        addMessage(message, true);
        
        // Add to recent queries
        if (!recentQueries.includes(message)) {
            recentQueries.unshift(message);
            if (recentQueries.length > 5) {
                recentQueries.pop();
            }
            updateRecentQueries();
        }
        
        // Clear input
        document.getElementById('assistant-input').value = '';
        
        // Update status
        document.getElementById('status-indicator').innerHTML = '<div class="loading"></div> Processing...';
        
        // Send request
        fetch(window.location.pathname, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: new URLSearchParams({
                'message': message,
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            // Add response message
            addMessage(data.response);
            document.getElementById('status-indicator').textContent = 'Ready';
        })
        .catch(error => {
            addMessage(`<div style="color: #dc3545;">Error: ${error.message}</div>`);
            document.getElementById('status-indicator').textContent = 'Error';
        });
    });
    
    // Auto-resize textarea
    const textarea = document.getElementById('assistant-input');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Focus input on load
    window.addEventListener('load', function() {
        document.getElementById('assistant-input').focus();
    });
</script>
{% endblock %}