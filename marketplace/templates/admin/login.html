{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<style type="text/css">
body.login {
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

.login #container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    max-width: 450px;
    width: 100%;
    padding: 0;
    overflow: hidden;
}

.login #header {
    background: linear-gradient(135deg, #0056b3 0%, #004494 100%);
    padding: 30px 40px;
    text-align: center;
    position: relative;
}

.login #header h1 {
    font-size: 24px;
    margin: 0;
    padding: 0;
}

.login #content {
    padding: 30px 40px;
}

.login .form-row {
    padding: 10px 0;
    border: none;
}

.login .form-row label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.login .form-row #id_username,
.login .form-row #id_password {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.login .form-row #id_username:focus,
.login .form-row #id_password:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

.login .submit-row {
    padding: 20px 0 0;
    margin: 0;
    border: none;
    text-align: center;
}

.login .submit-row input {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 5px;
    background: #0056b3;
    transition: background 0.2s ease;
}

.login .submit-row input:hover {
    background: #004494;
}

.login .errornote {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 12px 15px;
    margin-bottom: 20px;
}

.login .messagelist {
    margin: 0 0 20px;
    padding: 0;
}

.login .messagelist li {
    border-radius: 5px;
    padding: 12px 15px;
    margin-bottom: 10px;
}

.login #branding {
    margin-bottom: 15px;
}

.login #branding h1 {
    color: white;
}

.login .password-reset-link {
    text-align: center;
    margin-top: 20px;
    font-size: 14px;
}

.login .password-reset-link a {
    color: #0056b3;
    text-decoration: none;
}

.login .password-reset-link a:hover {
    text-decoration: underline;
}
</style>
{% endblock %}

{% block bodyclass %}login{% endblock %}

{% block usertools %}{% endblock %}

{% block nav-global %}{% endblock %}

{% block nav-sidebar %}{% endblock %}

{% block content_title %}{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block content %}
<div id="content-main">
    {% if form.errors and not form.non_field_errors %}
    <p class="errornote">
        {% if form.errors.items|length == 1 %}{% translate "Please correct the error below." %}{% else %}{% translate "Please correct the errors below." %}{% endif %}
    </p>
    {% endif %}

    {% if form.non_field_errors %}
    {% for error in form.non_field_errors %}
    <p class="errornote">
        {{ error }}
    </p>
    {% endfor %}
    {% endif %}

    <div id="content-main">
        <form action="{{ app_path }}" method="post" id="login-form">{% csrf_token %}
            <div class="form-row">
                {{ form.username.errors }}
                <label for="id_username">{{ form.username.label }}:</label>
                {{ form.username }}
            </div>
            <div class="form-row">
                {{ form.password.errors }}
                <label for="id_password">{{ form.password.label }}:</label>
                {{ form.password }}
                <input type="hidden" name="next" value="{{ next }}">
            </div>
            <div class="submit-row">
                <input type="submit" value="{% translate 'Log in' %}">
            </div>
        </form>

        <div class="password-reset-link">
            <a href="{% url 'admin_password_reset' %}">{% translate 'Forgotten your password or username?' %}</a>
        </div>
    </div>
</div>
{% endblock %}