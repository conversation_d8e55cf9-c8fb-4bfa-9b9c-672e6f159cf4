{% extends 'marketplace/base.html' %}

{% block title %}{{ category.name }} - Piata.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'marketplace:home' %}" class="text-gray-700 hover:text-primary">
                    <i class="fas fa-home mr-2"></i>Acasă
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <a href="{% url 'marketplace:categories' %}" class="text-gray-700 hover:text-primary">Categorii</a>
                </div>
            </li>
            {% if category.parent %}
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <a href="{% url 'marketplace:category_detail' category.parent.slug %}" class="text-gray-700 hover:text-primary">{{ category.parent.name }}</a>
                </div>
            </li>
            {% endif %}
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <span class="text-gray-500">{{ category.name }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Category Header -->
    <div class="bg-white rounded-lg shadow-md p-8 mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mr-6">
                    <i class="{{ category.icon|default:'fas fa-folder' }} text-3xl text-primary"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">{{ category.name }}</h1>
                    {% if category.description %}
                    <p class="text-gray-600 mb-4">{{ category.description }}</p>
                    {% endif %}
                    <p class="text-gray-500">
                        <i class="fas fa-list mr-2"></i>0 anunțuri în această categorie
                    </p>
                </div>
            </div>
            <a href="{% url 'marketplace:add_listing' %}?category={{ category.id }}" 
               class="bg-accent text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                <i class="fas fa-plus mr-2"></i>Adaugă anunț
            </a>
        </div>
    </div>

    <!-- Subcategories Section -->
    {% if subcategories %}
    <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-700 mb-4">Subcategorii în {{ category.name }}:</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {% for subcat in subcategories %}
            <a href="{% url 'marketplace:category_detail' subcat.slug %}" 
               class="block bg-gray-100 p-4 rounded-lg hover:bg-gray-200 transition-colors text-center">
                <i class="{{ subcat.icon|default:'fas fa-folder-open' }} text-2xl text-primary mb-2"></i>
                <h3 class="text-md font-semibold text-gray-800">{{ subcat.name }}</h3>
                <p class="text-sm text-gray-500">0 anunțuri</p>
            </a>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Caută în {{ category.name }}</label>
                <input type="text" name="search" value="{{ request.GET.search }}" 
                       placeholder="Caută anunțuri..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            
            <!-- Price Range -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Preț minim</label>
                <input type="number" name="min_price" value="{{ request.GET.min_price }}" 
                       placeholder="0" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Preț maxim</label>
                <input type="number" name="max_price" value="{{ request.GET.max_price }}" 
                       placeholder="Orice preț" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            
            <!-- Location -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Locație</label>
                <input type="text" name="location" value="{{ request.GET.location }}" 
                       placeholder="Oraș, județ..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            
            <!-- Sort -->
            <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Sortează după</label>
                <select name="sort" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="-created_at" {% if request.GET.sort == '-created_at' %}selected{% endif %}>Cele mai recente</option>
                    <option value="created_at" {% if request.GET.sort == 'created_at' %}selected{% endif %}>Cele mai vechi</option>
                    <option value="price" {% if request.GET.sort == 'price' %}selected{% endif %}>Preț crescător</option>
                    <option value="-price" {% if request.GET.sort == '-price' %}selected{% endif %}>Preț descrescător</option>
                    <option value="title" {% if request.GET.sort == 'title' %}selected{% endif %}>Alfabetic</option>
                </select>
            </div>
            
            <!-- Filter Buttons -->
            <div class="md:col-span-2 flex space-x-4 items-end">
                <button type="submit" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>Filtrează
                </button>
                <a href="{% url 'marketplace:category_detail' category.slug %}" 
                   class="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-times mr-2"></i>Resetează
                </a>
            </div>
        </form>
    </div>

    <!-- Listings Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% if listings %}
            {% for listing in listings %}
            <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                <a href="{% url 'marketplace:listing_detail' listing.slug %}" class="block">
                    <!-- Image -->
                    <div class="h-48 bg-gray-200 relative">
                        <div class="w-full h-full flex items-center justify-center text-gray-400">
                            <i class="fas fa-image text-4xl"></i>
                        </div>
                        
                        <!-- Price Badge -->
                        <div class="absolute top-3 right-3 bg-black bg-opacity-75 text-white px-3 py-1 rounded-lg">
                            <span class="font-semibold">0 RON</span>
                        </div>
                    </div>
                    
                    <!-- Content -->
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">{{ listing.title }}</h3>
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ listing.description }}</p>
                        
                        <!-- Meta Info -->
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <div class="flex items-center">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                <span>{{ listing.location }}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-clock mr-1"></i>
                                <span>{{ listing.created_at|timesince }}</span>
                            </div>
                        </div>
                        
                        <!-- User -->
                        <div class="flex items-center mt-3 pt-3 border-t border-gray-100">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-sm text-gray-600"></i>
                            </div>
                            <span class="text-sm text-gray-600">{{ listing.user.username }}</span>
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-span-full">
                <div class="bg-white rounded-lg shadow-md p-12 text-center">
                    <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">Nu am găsit anunțuri</h3>
                    <p class="text-gray-500 mb-6">Nu există anunțuri în această categorie cu criteriile selectate.</p>
                    <a href="{% url 'marketplace:add_listing' %}?category={{ category.id }}" 
                       class="inline-block bg-accent text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-plus mr-2"></i>Fii primul care adaugă un anunț
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}