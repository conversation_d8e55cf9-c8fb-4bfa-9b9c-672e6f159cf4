{% extends 'marketplace/base.html' %}

{% block title %}{{ profile_user.username }} - Piata.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'marketplace:home' %}" class="text-gray-700 hover:text-primary">
                    <i class="fas fa-home mr-2"></i>Acasă
                </a>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <span class="text-gray-500">Profil {{ profile_user.username }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- User Profile Card -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="text-center">
                    <div class="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user text-3xl text-gray-600"></i>
                    </div>
                    <h1 class="text-2xl font-bold text-gray-800 mb-2">{{ profile_user.username }}</h1>
                    <p class="text-gray-600 mb-4">Membru din {{ profile_user.date_joined|date:"M Y" }}</p>
                    
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-primary">{{ user_listings.count }}</div>
                            <div class="text-sm text-gray-600">Anunțuri active</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-green-600">
                                {% if user_profile.verified %}
                                    <i class="fas fa-check-circle"></i>
                                {% else %}
                                    <i class="fas fa-times-circle text-gray-400"></i>
                                {% endif %}
                            </div>
                            <div class="text-sm text-gray-600">
                                {% if user_profile.verified %}Verificat{% else %}Neverificat{% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if user.is_authenticated and user != profile_user %}
                <div class="mt-6">
                    <button class="w-full bg-primary text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-envelope mr-2"></i>Trimite mesaj
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- User Listings -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-list mr-2"></i>Anunțuri ({{ user_listings.count }})
                </h2>
                
                {% if user_listings %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {% for listing in user_listings %}
                        <div class="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                            <a href="{% url 'marketplace:listing_detail' listing.id %}" class="block">
                                <div class="h-32 bg-gray-200">
                                    {% if listing.main_image %}
                                    <img src="{{ listing.main_image.image.url }}" alt="{{ listing.title }}" 
                                         class="w-full h-full object-cover">
                                    {% else %}
                                    <div class="w-full h-full flex items-center justify-center">
                                        <i class="fas fa-image text-4xl text-gray-400"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div class="p-4">
                                    <h3 class="font-semibold text-gray-800 mb-2 line-clamp-2">{{ listing.title }}</h3>
                                    <div class="flex items-center justify-between">
                                        <span class="text-lg font-bold text-primary">
                                            {{ listing.price }} {{ listing.currency|upper }}
                                        </span>
                                        <span class="text-sm text-gray-500">
                                            {{ listing.created_at|date:"d.m.Y" }}
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-600 mt-2">
                                        <i class="fas fa-map-marker-alt mr-1"></i>{{ listing.location }}
                                    </div>
                                </div>
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12">
                        <i class="fas fa-inbox text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">Niciun anunț activ</h3>
                        <p class="text-gray-500">Acest utilizator nu are anunțuri publicate în acest moment.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
