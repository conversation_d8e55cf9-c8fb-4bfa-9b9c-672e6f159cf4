{% extends 'marketplace/base.html' %}
{% load static %}

{% block title %}Coș Credite - Piata.ro{% endblock %}

{% block extra_head %}
<script src="https://js.stripe.com/v3/"></script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                <i class="fas fa-shopping-cart mr-3 text-primary"></i>
                Coș de Cumpărături
            </h1>
            <p class="text-xl text-gray-600">Adaugă credite în coș și finalizează comanda</p>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
            
            <!-- Credit Packages (Left Column) -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">
                        <i class="fas fa-coins mr-2 text-yellow-500"></i>
                        Pachete de Credite Disponibile
                    </h2>
                    
                    <!-- Current Credits Display -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-semibold text-blue-900">Creditele tale actuale</h3>
                                <p class="text-sm text-blue-700">Folosești creditele pentru a promova anunțurile</p>
                            </div>
                            <div class="text-3xl font-bold text-blue-900">
                                {{ user.profile.credits_balance|floatformat:1 }}
                                <span class="text-lg font-normal">credite</span>
                            </div>
                        </div>
                    </div>

                    <!-- Credit Packages Grid -->
                    <div class="grid md:grid-cols-2 gap-4" id="packages-grid">
                        <!-- 1 Credit Package -->
                        <div class="credit-package border-2 border-gray-200 rounded-lg p-4 hover:border-primary transition-all duration-200 cursor-pointer" 
                             data-credits="1" data-price-ron="5" data-price-eur="1">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-primary mb-2">1</div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Credit de Test</h3>
                                <div class="text-sm text-gray-600 mb-3">Perfect pentru a testa funcționalitatea</div>
                                <div class="flex justify-between items-center">
                                    <div class="text-xl font-bold text-gray-900">
                                        <span class="price-ron">5 RON</span>
                                        <span class="price-eur hidden">1 EUR</span>
                                    </div>
                                    <button class="add-to-cart bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors">
                                        <i class="fas fa-plus mr-1"></i>Adaugă
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 5 Credits Package -->
                        <div class="credit-package border-2 border-gray-200 rounded-lg p-4 hover:border-primary transition-all duration-200 cursor-pointer" 
                             data-credits="5" data-price-ron="25" data-price-eur="5">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-primary mb-2">5</div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Pachet Starter</h3>
                                <div class="text-sm text-gray-600 mb-3">Ideal pentru câteva promovări</div>
                                <div class="flex justify-between items-center">
                                    <div class="text-xl font-bold text-gray-900">
                                        <span class="price-ron">25 RON</span>
                                        <span class="price-eur hidden">5 EUR</span>
                                    </div>
                                    <button class="add-to-cart bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors">
                                        <i class="fas fa-plus mr-1"></i>Adaugă
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 10 Credits Package -->
                        <div class="credit-package border-2 border-green-200 rounded-lg p-4 hover:border-green-500 transition-all duration-200 cursor-pointer relative bg-green-50" 
                             data-credits="10" data-price-ron="45" data-price-eur="9">
                            <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                <span class="bg-green-500 text-white text-xs font-bold px-3 py-1 rounded-full">ECONOMIE 10%</span>
                            </div>
                            <div class="text-center pt-2">
                                <div class="text-3xl font-bold text-green-600 mb-2">10</div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Pachet Recomandat</h3>
                                <div class="text-sm text-gray-600 mb-3">Pentru utilizatori activi</div>
                                <div class="flex justify-between items-center">
                                    <div class="text-xl font-bold text-gray-900">
                                        <span class="price-ron">45 RON</span>
                                        <span class="price-eur hidden">9 EUR</span>
                                        <div class="text-xs text-gray-500 line-through">
                                            <span class="original-price-ron">50 RON</span>
                                            <span class="original-price-eur hidden">10 EUR</span>
                                        </div>
                                    </div>
                                    <button class="add-to-cart bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                                        <i class="fas fa-plus mr-1"></i>Adaugă
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 20 Credits Package -->
                        <div class="credit-package border-2 border-purple-200 rounded-lg p-4 hover:border-purple-500 transition-all duration-200 cursor-pointer relative bg-purple-50" 
                             data-credits="20" data-price-ron="80" data-price-eur="16">
                            <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                <span class="bg-purple-500 text-white text-xs font-bold px-3 py-1 rounded-full">ECONOMIE 20%</span>
                            </div>
                            <div class="text-center pt-2">
                                <div class="text-3xl font-bold text-purple-600 mb-2">20</div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Pachet Popular</h3>
                                <div class="text-sm text-gray-600 mb-3">Cel mai ales de clienți</div>
                                <div class="flex justify-between items-center">
                                    <div class="text-xl font-bold text-gray-900">
                                        <span class="price-ron">80 RON</span>
                                        <span class="price-eur hidden">16 EUR</span>
                                        <div class="text-xs text-gray-500 line-through">
                                            <span class="original-price-ron">100 RON</span>
                                            <span class="original-price-eur hidden">20 EUR</span>
                                        </div>
                                    </div>
                                    <button class="add-to-cart bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors">
                                        <i class="fas fa-plus mr-1"></i>Adaugă
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 50 Credits Package -->
                        <div class="credit-package border-2 border-gold-200 rounded-lg p-4 hover:border-yellow-500 transition-all duration-200 cursor-pointer relative bg-yellow-50 md:col-span-2" 
                             data-credits="50" data-price-ron="175" data-price-eur="35">
                            <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                <span class="bg-yellow-500 text-white text-xs font-bold px-3 py-1 rounded-full">ECONOMIE 30%</span>
                            </div>
                            <div class="text-center pt-2">
                                <div class="text-4xl font-bold text-yellow-600 mb-2">50</div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Pachet Business</h3>
                                <div class="text-sm text-gray-600 mb-3">Pentru utilizatori intensivi și agenții</div>
                                <div class="flex justify-center items-center space-x-6">
                                    <div class="text-2xl font-bold text-gray-900">
                                        <span class="price-ron">175 RON</span>
                                        <span class="price-eur hidden">35 EUR</span>
                                        <div class="text-sm text-gray-500 line-through">
                                            <span class="original-price-ron">250 RON</span>
                                            <span class="original-price-eur hidden">50 EUR</span>
                                        </div>
                                    </div>
                                    <button class="add-to-cart bg-yellow-600 text-white px-6 py-3 rounded-md hover:bg-yellow-700 transition-colors text-lg">
                                        <i class="fas fa-plus mr-2"></i>Adaugă în Coș
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shopping Cart (Right Column) -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6 sticky top-4">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">
                        <i class="fas fa-shopping-cart mr-2 text-primary"></i>
                        Coșul tău
                    </h3>
                    
                    <!-- Empty Cart State -->
                    <div id="empty-cart" class="text-center py-8">
                        <i class="fas fa-shopping-cart text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500 mb-4">Coșul tău este gol</p>
                        <p class="text-sm text-gray-400">Adaugă pachete de credite pentru a continua</p>
                    </div>

                    <!-- Cart Items -->
                    <div id="cart-items" class="hidden">
                        <div class="space-y-3 mb-4" id="cart-items-list">
                            <!-- Cart items will be added here dynamically -->
                        </div>

                        <!-- Currency Selector -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Moneda de plată:</label>
                            <div class="grid grid-cols-2 gap-2">
                                <label class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                                    <input type="radio" name="currency" value="ron" class="h-4 w-4 text-primary" checked>
                                    <span class="ml-2 text-sm font-medium">RON</span>
                                </label>
                                <label class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                                    <input type="radio" name="currency" value="eur" class="h-4 w-4 text-primary">
                                    <span class="ml-2 text-sm font-medium">EUR</span>
                                </label>
                            </div>
                        </div>

                        <!-- Cart Summary -->
                        <div class="border-t pt-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-gray-600">Total credite:</span>
                                <span class="font-semibold" id="total-credits">0</span>
                            </div>
                            <div class="flex justify-between items-center mb-4">
                                <span class="text-lg font-semibold text-gray-900">Total de plată:</span>
                                <span class="text-xl font-bold text-primary" id="total-amount">0 RON</span>
                            </div>

                            <!-- Checkout Button -->
                            <button id="checkout-btn" class="w-full bg-primary text-white py-3 px-4 rounded-lg font-semibold hover:bg-primary-dark transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-credit-card mr-2"></i>
                                Continuă la Plată
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Security Info -->
                <div class="bg-gray-50 rounded-lg p-4 mt-4">
                    <h4 class="font-semibold text-gray-800 mb-2">
                        <i class="fas fa-shield-alt mr-2 text-green-500"></i>
                        Plată Securizată
                    </h4>
                    <div class="text-sm text-gray-600 space-y-1">
                        <p>• Procesăm plățile prin Stripe</p>
                        <p>• SSL encryption 256-bit</p>
                        <p>• Nu stocăm datele cardului</p>
                        <p>• Suport pentru toate cardurile</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for Stripe checkout -->
<form id="stripe-form" method="POST" action="{% url 'marketplace:process_payment' %}" style="display: none;">
    {% csrf_token %}
    <input type="hidden" name="cart_data" id="cart-data">
    <input type="hidden" name="currency" id="selected-currency" value="ron">
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let cart = [];
    
    const emptyCart = document.getElementById('empty-cart');
    const cartItems = document.getElementById('cart-items');
    const cartItemsList = document.getElementById('cart-items-list');
    const totalCredits = document.getElementById('total-credits');
    const totalAmount = document.getElementById('total-amount');
    const checkoutBtn = document.getElementById('checkout-btn');
    const currencyRadios = document.querySelectorAll('input[name="currency"]');
    
    // Add to cart functionality
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function() {
            const package = this.closest('.credit-package');
            const credits = parseInt(package.dataset.credits);
            const priceRon = parseInt(package.dataset.priceRon);
            const priceEur = parseInt(package.dataset.priceEur);
            
            // Check if item already in cart
            const existingItem = cart.find(item => item.credits === credits);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    credits: credits,
                    priceRon: priceRon,
                    priceEur: priceEur,
                    quantity: 1
                });
            }
            
            updateCartDisplay();
            
            // Visual feedback
            this.innerHTML = '<i class="fas fa-check mr-1"></i>Adăugat';
            this.classList.add('bg-green-500');
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-plus mr-1"></i>Adaugă';
                this.classList.remove('bg-green-500');
            }, 1000);
        });
    });
    
    // Currency change handler
    currencyRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            updatePriceDisplay();
            updateCartDisplay();
            document.getElementById('selected-currency').value = this.value;
        });
    });
    
    function updatePriceDisplay() {
        const selectedCurrency = document.querySelector('input[name="currency"]:checked').value;
        
        if (selectedCurrency === 'eur') {
            document.querySelectorAll('.price-ron').forEach(el => el.classList.add('hidden'));
            document.querySelectorAll('.price-eur').forEach(el => el.classList.remove('hidden'));
            document.querySelectorAll('.original-price-ron').forEach(el => el.classList.add('hidden'));
            document.querySelectorAll('.original-price-eur').forEach(el => el.classList.remove('hidden'));
        } else {
            document.querySelectorAll('.price-ron').forEach(el => el.classList.remove('hidden'));
            document.querySelectorAll('.price-eur').forEach(el => el.classList.add('hidden'));
            document.querySelectorAll('.original-price-ron').forEach(el => el.classList.remove('hidden'));
            document.querySelectorAll('.original-price-eur').forEach(el => el.classList.add('hidden'));
        }
    }
    
    function updateCartDisplay() {
        if (cart.length === 0) {
            emptyCart.classList.remove('hidden');
            cartItems.classList.add('hidden');
            checkoutBtn.disabled = true;
            return;
        }
        
        emptyCart.classList.add('hidden');
        cartItems.classList.remove('hidden');
        checkoutBtn.disabled = false;
        
        // Update cart items list
        cartItemsList.innerHTML = '';
        let totalCreditsCount = 0;
        let totalPrice = 0;
        const selectedCurrency = document.querySelector('input[name="currency"]:checked').value;
        
        cart.forEach((item, index) => {
            const price = selectedCurrency === 'eur' ? item.priceEur : item.priceRon;
            const itemTotal = price * item.quantity;
            totalCreditsCount += item.credits * item.quantity;
            totalPrice += itemTotal;
            
            const cartItemHtml = `
                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                    <div class="flex-1">
                        <div class="font-medium text-gray-900">${item.credits} credite</div>
                        <div class="text-sm text-gray-500">${price} ${selectedCurrency.toUpperCase()} x ${item.quantity}</div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="decrease-qty text-gray-400 hover:text-gray-600" data-index="${index}">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="w-8 text-center">${item.quantity}</span>
                        <button class="increase-qty text-gray-400 hover:text-gray-600" data-index="${index}">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="remove-item text-red-400 hover:text-red-600 ml-2" data-index="${index}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            cartItemsList.insertAdjacentHTML('beforeend', cartItemHtml);
        });
        
        // Update totals
        totalCredits.textContent = totalCreditsCount;
        totalAmount.textContent = `${totalPrice} ${selectedCurrency.toUpperCase()}`;
        
        // Add event listeners for quantity controls
        addQuantityEventListeners();
    }
    
    function addQuantityEventListeners() {
        document.querySelectorAll('.increase-qty').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.dataset.index);
                cart[index].quantity += 1;
                updateCartDisplay();
            });
        });
        
        document.querySelectorAll('.decrease-qty').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.dataset.index);
                if (cart[index].quantity > 1) {
                    cart[index].quantity -= 1;
                } else {
                    cart.splice(index, 1);
                }
                updateCartDisplay();
            });
        });
        
        document.querySelectorAll('.remove-item').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.dataset.index);
                cart.splice(index, 1);
                updateCartDisplay();
            });
        });
    }
    
    // Checkout functionality
    checkoutBtn.addEventListener('click', function() {
        if (cart.length === 0) return;
        
        document.getElementById('cart-data').value = JSON.stringify(cart);
        document.getElementById('stripe-form').submit();
    });
    
    // Initialize price display
    updatePriceDisplay();
});
</script>
{% endblock %}
