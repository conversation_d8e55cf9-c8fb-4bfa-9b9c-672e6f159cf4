{% extends 'marketplace/base.html' %}

{% block title %}Editează profilul - Piata.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-user-edit mr-3 text-primary"></i>Editează profilul
                </h1>
                <p class="text-gray-600">Actualizează informațiile tale personale</p>
            </div>
            <a href="{% url 'marketplace:profile' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Înapoi la profil
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Edit Form -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md p-6">
                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-user mr-2 text-primary"></i>Informații de bază
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="{{ user_form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ user_form.first_name.label }}
                                </label>
                                {{ user_form.first_name }}
                                {% if user_form.first_name.errors %}
                                    <div class="text-red-600 text-sm mt-1">{{ user_form.first_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ user_form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ user_form.last_name.label }}
                                </label>
                                {{ user_form.last_name }}
                                {% if user_form.last_name.errors %}
                                    <div class="text-red-600 text-sm mt-1">{{ user_form.last_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <label for="{{ user_form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ user_form.email.label }}
                            </label>
                            {{ user_form.email }}
                            {% if user_form.email.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ user_form.email.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Profile Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-address-card mr-2 text-primary"></i>Informații profil
                        </h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="{{ profile_form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ profile_form.phone.label }}
                                </label>
                                {{ profile_form.phone }}
                                {% if profile_form.phone.help_text %}
                                    <p class="text-sm text-gray-500 mt-1">{{ profile_form.phone.help_text }}</p>
                                {% endif %}
                                {% if profile_form.phone.errors %}
                                    <div class="text-red-600 text-sm mt-1">{{ profile_form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ profile_form.location.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ profile_form.location.label }}
                                </label>
                                {{ profile_form.location }}
                                {% if profile_form.location.help_text %}
                                    <p class="text-sm text-gray-500 mt-1">{{ profile_form.location.help_text }}</p>
                                {% endif %}
                                {% if profile_form.location.errors %}
                                    <div class="text-red-600 text-sm mt-1">{{ profile_form.location.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ profile_form.bio.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ profile_form.bio.label }}
                                </label>
                                {{ profile_form.bio }}
                                {% if profile_form.bio.help_text %}
                                    <p class="text-sm text-gray-500 mt-1">{{ profile_form.bio.help_text }}</p>
                                {% endif %}
                                {% if profile_form.bio.errors %}
                                    <div class="text-red-600 text-sm mt-1">{{ profile_form.bio.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                        <a href="{% url 'marketplace:profile' %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                            Anulează
                        </a>
                        <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-save mr-2"></i>Salvează modificările
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Help Section -->
        <div class="lg:col-span-1">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="fas fa-info-circle mr-2"></i>Sfaturi pentru profil
                </h3>
                <ul class="text-blue-700 space-y-3 text-sm">
                    <li class="flex items-start">
                        <i class="fas fa-phone mr-2 mt-1 text-xs"></i>
                        <span>Adaugă numărul de telefon pentru a fi contactat mai ușor de către cumpărători</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-map-marker-alt mr-2 mt-1 text-xs"></i>
                        <span>Specifică orașul pentru a ajuta utilizatorii să găsească anunțurile locale</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-user-circle mr-2 mt-1 text-xs"></i>
                        <span>O descriere scurtă te va ajuta să câștigi încrederea utilizatorilor</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-shield-alt mr-2 mt-1 text-xs"></i>
                        <span>Nu partaja informații personale sensibile în descrierea publică</span>
                    </li>
                </ul>
            </div>

            <!-- Profile Stats -->
            <div class="bg-white rounded-lg shadow-md p-6 mt-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-chart-bar mr-2 text-primary"></i>Statistici
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Anunțuri active:</span>
                        <span class="font-semibold">{{ user.listings.count }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Membru din:</span>
                        <span class="font-semibold">{{ user.date_joined|date:"F Y" }}</span>
                    </div>
                    {% if user.profile.is_premium %}
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Status:</span>
                        <span class="bg-gold text-white px-2 py-1 rounded text-xs font-semibold">Premium</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
