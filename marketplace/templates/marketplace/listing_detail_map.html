<!-- Interactive Map for Listing Detail -->
<div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h3 class="text-xl font-semibold text-gray-800 mb-4">
        <i class="fas fa-map-marker-alt text-primary mr-2"></i>Locație
    </h3>
    
    {% if listing.latitude and listing.longitude %}
        <!-- Map Container -->
        <div id="listing-map" class="w-full h-64 rounded-lg mb-4"></div>
        
        <!-- Location Details -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
                <strong>Adresă:</strong> {{ listing.location|default:"Nu este specificată" }}
            </div>
            <div>
                <strong>Oraș:</strong> {{ listing.city|default:"Nu este specificat" }}
            </div>
            {% if listing.county %}
            <div>
                <strong>Județ:</strong> {{ listing.county }}
            </div>
            {% endif %}
            <div>
                <strong>Coordonate:</strong> {{ listing.latitude|floatformat:4 }}, {{ listing.longitude|floatformat:4 }}
            </div>
        </div>
        
        <!-- Distance Calculator -->
        <div class="mt-4 p-4 bg-blue-50 rounded-lg">
            <h4 class="font-semibold text-blue-900 mb-2">Calculează distanța</h4>
            <div class="flex gap-2">
                <input type="text" id="user-location" placeholder="Introdu locația ta..." 
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm">
                <button onclick="calculateDistance()" 
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700">
                    <i class="fas fa-route mr-1"></i>Calculează
                </button>
            </div>
            <div id="distance-result" class="mt-2 text-sm text-blue-800"></div>
        </div>
        
    {% else %}
        <!-- No Coordinates Available -->
        <div class="text-center py-8 text-gray-500">
            <i class="fas fa-map-marker-alt text-4xl mb-4 text-gray-300"></i>
            <p>Locația exactă nu este disponibilă pentru acest anunț.</p>
            {% if listing.location %}
            <p class="mt-2"><strong>Locație generală:</strong> {{ listing.location }}</p>
            {% endif %}
        </div>
    {% endif %}
</div>

<!-- Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
{% if listing.latitude and listing.longitude %}
// Initialize map
const map = L.map('listing-map').setView([{{ listing.latitude }}, {{ listing.longitude }}], 13);

// Add OpenStreetMap tiles
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
}).addTo(map);

// Add marker for listing
const listingMarker = L.marker([{{ listing.latitude }}, {{ listing.longitude }}])
    .addTo(map)
    .bindPopup(`
        <div class="text-center">
            <strong>{{ listing.title|escapejs }}</strong><br>
            <small>{{ listing.location|escapejs }}</small><br>
            <span class="text-green-600 font-bold">{{ listing.price }} {{ listing.currency }}</span>
        </div>
    `)
    .openPopup();

// Custom marker icon
const customIcon = L.divIcon({
    html: '<i class="fas fa-map-marker-alt text-2xl text-red-500"></i>',
    iconSize: [20, 20],
    className: 'custom-div-icon'
});
listingMarker.setIcon(customIcon);

// Distance calculation function
async function calculateDistance() {
    const userLocation = document.getElementById('user-location').value.trim();
    const resultDiv = document.getElementById('distance-result');
    
    if (!userLocation) {
        resultDiv.innerHTML = '<span class="text-red-600">Te rugăm să introduci o locație.</span>';
        return;
    }
    
    resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Calculez distanța...';
    
    try {
        // Geocode user location
        const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(userLocation + ', România')}&limit=1`);
        const data = await response.json();
        
        if (data.length === 0) {
            resultDiv.innerHTML = '<span class="text-red-600">Locația nu a fost găsită. Încearcă o altă adresă.</span>';
            return;
        }
        
        const userLat = parseFloat(data[0].lat);
        const userLon = parseFloat(data[0].lon);
        const listingLat = {{ listing.latitude }};
        const listingLon = {{ listing.longitude }};
        
        // Calculate distance using Haversine formula
        const distance = calculateHaversineDistance(userLat, userLon, listingLat, listingLon);
        
        // Add user marker to map
        if (window.userMarker) {
            map.removeLayer(window.userMarker);
        }
        
        window.userMarker = L.marker([userLat, userLon])
            .addTo(map)
            .bindPopup(`<strong>Locația ta</strong><br>${data[0].display_name}`);
        
        // Draw line between locations
        if (window.distanceLine) {
            map.removeLayer(window.distanceLine);
        }
        
        window.distanceLine = L.polyline([
            [userLat, userLon],
            [listingLat, listingLon]
        ], {color: 'blue', weight: 3, opacity: 0.7}).addTo(map);
        
        // Fit map to show both markers
        const group = new L.featureGroup([listingMarker, window.userMarker]);
        map.fitBounds(group.getBounds().pad(0.1));
        
        // Display result
        resultDiv.innerHTML = `
            <div class="flex items-center justify-between">
                <span><i class="fas fa-route mr-1"></i><strong>Distanța:</strong> ${distance.toFixed(1)} km</span>
                <span><i class="fas fa-car mr-1"></i>~${Math.round(distance * 1.5)} min cu mașina</span>
            </div>
        `;
        
    } catch (error) {
        console.error('Distance calculation error:', error);
        resultDiv.innerHTML = '<span class="text-red-600">Eroare la calcularea distanței. Încearcă din nou.</span>';
    }
}

// Haversine distance calculation
function calculateHaversineDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}

// Allow Enter key to calculate distance
document.getElementById('user-location').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        calculateDistance();
    }
});
{% endif %}
</script>

<style>
.custom-div-icon {
    background: none;
    border: none;
}

.leaflet-popup-content {
    margin: 8px 12px;
    line-height: 1.4;
}

#listing-map {
    border: 2px solid #e5e7eb;
}
</style>