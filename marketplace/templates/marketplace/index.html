{% extends 'marketplace/base.html' %}

{% block title %}Piata.ro - Anunțuri gratuite România{% endblock %}

{% block content %}
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-primary to-blue-600 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Găsește tot ce ai nevoie pe Piata.ro
            </h1>
            <p class="text-xl md:text-2xl mb-8 opacity-90">
                Cea mai mare platformă de anunțuri din România
            </p>
            
            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                <div class="text-center">
                    <div id="totalListingsStat" class="text-3xl md:text-4xl font-bold mb-2">0</div>
                    <div class="text-lg opacity-80">Anunțuri active</div>
                </div>
                <div class="text-center">
                    <div id="totalCategoriesStat" class="text-3xl md:text-4xl font-bold mb-2">0</div>
                    <div class="text-lg opacity-80">Categorii</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold mb-2">1000+</div>
                    <div class="text-lg opacity-80">Utilizatori activi</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Section -->
    <section class="relative -mt-8 mb-12">
        <div class="container mx-auto px-4">
            <div class="bg-white rounded-2xl shadow-xl p-6 md:p-8">
                <form id="homeSearchForm" class="grid grid-cols-1 md:grid-cols-4 gap-4" action="{% url 'marketplace:listings' %}" method="GET">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Ce cauți?</label>
                        <input type="text" name="search" placeholder="Caută anunțuri..." 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Categoria</label>
                        <select name="category" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Toate categoriile</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-primary text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300 font-semibold">
                            <i class="fas fa-search mr-2"></i>Caută
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Popular Categories -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-800">
                Categorii populare
            </h2>
            <div id="categoriesGrid" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                {# Categories will be loaded dynamically by JavaScript #}
            </div>
        </div>
    </section>

    <!-- Featured Listings -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800">
                    <i class="fas fa-star text-yellow-500 mr-3"></i>Anunțuri promovate
                </h2>
                <a href="{% url 'marketplace:listings' %}?is_featured=true" class="text-primary hover:text-blue-700 font-semibold">
                    Vezi toate <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            <div id="featuredListings" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {# Featured listings will be loaded dynamically by JavaScript #}
            </div>
        </div>
    </section>

    <!-- Recent Listings -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800">
                    <i class="fas fa-clock text-blue-500 mr-3"></i>Anunțuri recente
                </h2>
                <a href="{% url 'marketplace:listings' %}" class="text-primary hover:text-blue-700 font-semibold">
                    Vezi toate <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            <div id="recentListings" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {# Recent listings will be loaded dynamically by JavaScript #}
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-gradient-to-r from-accent to-green-600 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">
                Vrei să vinzi ceva?
            </h2>
            <p class="text-xl mb-8 opacity-90">
                Adaugă un anunț gratuit și ajunge la mii de cumpărători potențiali
            </p>
            <a href="{% url 'marketplace:add_listing' %}" class="inline-block bg-white text-accent px-8 py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition duration-300">
                <i class="fas fa-plus mr-2"></i>Adaugă anunț gratuit
            </a>
        </div>
    </section>
{% endblock %}

{% block extra_scripts %}
<script>
    // API Base URL
    const API_BASE_URL = '/api'; // Used by fetchAPI

    // Utility functions (formatPrice, formatDate, truncateText are used by populateListings)
    // showLoading and hideLoading are defined in base.html and use the spinner from base.html

    function formatPrice(price, currency = "RON") { // Added currency parameter
        if (price === null || price === undefined) return "N/A";
        return new Intl.NumberFormat('ro-RO', { style: 'currency', currency: currency }).format(price);
    }

    function formatDate(dateString) {
        if (!dateString) return "N/A";
        const date = new Date(dateString);
        return date.toLocaleDateString('ro-RO', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    function truncateText(text, maxLength = 50) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    // Category icons and colors mapping
    const categoryIcons = {
        'Imobiliare': 'fas fa-home',
        'Auto moto': 'fas fa-car',
        'Locuri de muncă': 'fas fa-briefcase',
        'Matrimoniale': 'fas fa-heart',
        'Servicii': 'fas fa-tools',
        'Electronice': 'fas fa-laptop',
        'Modă și accesorii': 'fas fa-tshirt',
        'Animale': 'fas fa-paw',
        'Casă și grădină': 'fas fa-home',
        'Timp liber și sport': 'fas fa-futbol',
        'Mamă și copil': 'fas fa-baby',
        'Cazare turism': 'fas fa-suitcase-rolling',
        'default': 'fas fa-tag'
    };

    const categoryColors = [
        'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
        'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500',
        'bg-orange-500', 'bg-cyan-500', 'bg-lime-500', 'bg-emerald-500'
    ];

    // API Functions
    async function fetchAPI(endpoint) {
        try {
            const response = await fetch(`${API_BASE_URL}${endpoint}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('API Error fetching ' + endpoint + ':', error);
            // Optionally display a user-friendly error message on the page
            return null;
        }
    }

    // Load Categories
    async function loadCategories() {
        const categories = await fetchAPI('/categories/');
        if (!categories) return;

        // Update category count for stats
        const totalCategoriesStatEl = document.getElementById('totalCategoriesStat');
        if (totalCategoriesStatEl) animateCounter(totalCategoriesStatEl, categories.length);


        // Populate search dropdown
        const searchSelect = document.querySelector('form#homeSearchForm select[name="category"]');
        if (searchSelect) {
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                searchSelect.appendChild(option);
            });
        }

        // Populate categories grid
        const categoriesGrid = document.getElementById('categoriesGrid');
        if (categoriesGrid) {
            categoriesGrid.innerHTML = ''; // Clear existing
            const parentCategories = categories.filter(c => !c.parent).slice(0, 12);
            parentCategories.forEach((category, index) => {
                const iconClass = category.icon || categoryIcons[category.name] || categoryIcons.default;
                const colorClass = categoryColors[index % categoryColors.length];
                
                // Use Django URL for category detail, but construct it carefully for JS
                // For simplicity, we'll use the direct slug-based URL structure
                const categoryDetailUrl = `/categorii/${category.slug}/`;

                categoriesGrid.innerHTML += `
                    <a href="${categoryDetailUrl}" class="group">
                        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition duration-300 text-center group-hover:transform group-hover:scale-105">
                            <div class="w-16 h-16 ${colorClass} rounded-full mx-auto mb-4 flex items-center justify-center">
                                <i class="${iconClass} text-white text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 group-hover:text-primary">
                                ${category.name}
                            </h3>
                            <p class="text-sm text-gray-500 mt-1">${category.listing_count || 0} anunțuri</p>
                        </div>
                    </a>
                `;
            });
        }
    }

    // Load Listings
    async function loadListings() {
        const listings = await fetchAPI('/listings/?limit=12'); // Fetch a limited number for homepage
        if (!listings || !listings.results) return; // Assuming API returns {count, next, previous, results}

        const allListings = listings.results;

        // Update total listings count for stats
        const totalListingsStatEl = document.getElementById('totalListingsStat');
        if (totalListingsStatEl && listings.count !== undefined) {
             animateCounter(totalListingsStatEl, listings.count);
        }


        // Filter featured and recent listings
        const featuredListings = allListings.filter(listing => listing.is_featured).slice(0, 4);
        // Sort by creation date for recent, assuming `created_at` is available and sortable
        const recentListings = [...allListings]
            .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
            .slice(0, 8);

        // Populate featured listings
        populateListings('featuredListings', featuredListings);
        
        // Populate recent listings
        populateListings('recentListings', recentListings);
    }

    function populateListings(containerId, listingsToPopulate) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        if (!listingsToPopulate || listingsToPopulate.length === 0) {
            container.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <i class="fas fa-inbox text-6xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500 text-lg">Nu există anunțuri momentan în această secțiune.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = listingsToPopulate.map(listing => {
            const listingDetailUrl = `/anunt/${listing.id}/`; // Frontend URL
            const image_url = listing.main_image ? listing.main_image : null; // Use main_image from API

            return `
                <div class="group">
                    <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition duration-300 group-hover:transform group-hover:scale-105 h-full flex flex-col">
                        <a href="${listingDetailUrl}" class="block">
                            <div class="relative">
                                ${image_url ? 
                                    `<img src="${image_url}" alt="${truncateText(listing.title, 30)}" class="w-full h-48 object-cover">` :
                                    `<div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-image text-gray-400 text-4xl"></i>
                                    </div>`
                                }
                                ${listing.is_featured ? 
                                    '<div class="absolute top-2 left-2 bg-accent text-white px-2 py-1 rounded-full text-xs font-semibold"><i class="fas fa-star mr-1"></i>Promovat</div>' : 
                                    ''
                                }
                                <div class="absolute top-2 right-2 bg-white/90 text-primary px-2 py-1 rounded-full text-sm font-bold">
                                    ${listing.price ? formatPrice(listing.price, listing.currency) : 'Preț la cerere'}
                                </div>
                            </div>
                        </a>
                        <div class="p-4 flex flex-col flex-grow">
                            <h3 class="font-semibold text-lg mb-2 text-gray-800 group-hover:text-primary line-clamp-2">
                                <a href="${listingDetailUrl}" class="hover:underline">${truncateText(listing.title, 60)}</a>
                            </h3>
                            <p class="text-gray-600 text-sm mb-1 line-clamp-1"><i class="fas fa-tag mr-1 text-gray-400"></i>${listing.category_name || 'N/A'}</p>
                            <p class="text-gray-600 text-sm mb-3 line-clamp-1">
                                <i class="fas fa-map-marker-alt mr-1 text-gray-400"></i>
                                ${listing.location || 'N/A'}
                            </p>
                            <div class="mt-auto flex items-center justify-between text-xs text-gray-500">
                                <span><i class="fas fa-eye mr-1"></i>${listing.views || 0}</span>
                                <span>${formatDate(listing.created_at)}</span>
                            </div>
                            <a href="${listingDetailUrl}" class="mt-3 block w-full bg-primary text-white text-center py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-sm">
                                Vezi detalii
                            </a>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // Navigation functions for dynamic elements (if any were to use these)
    // The category grid items are now direct <a> tags.
    // Listing items are also direct <a> tags.

    // Animate counters for stats
    function animateCounter(element, target) {
        if (!element) return;
        const current = parseInt(element.textContent) || 0;
        const duration = 1000; // 1 second
        const frameRate = 60; // 60fps
        const totalFrames = Math.round(duration / (1000 / frameRate));
        const increment = (target - current) / totalFrames;
        
        let currentFrame = 0;
        function updateCounter() {
            currentFrame++;
            const newValue = Math.round(current + (increment * currentFrame));
            element.textContent = newValue;
            if (currentFrame < totalFrames) {
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target; // Ensure final value is exact
            }
        }
        requestAnimationFrame(updateCounter);
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', async function() {
        if (typeof showLoading === 'function') showLoading();
        
        try {
            await Promise.all([
                loadCategories(),
                loadListings()
            ]);
        } catch (error) {
            console.error('Error loading page data:', error);
        }
        
        if (typeof hideLoading === 'function') hideLoading();
    });

    // Search form submission (uses action attribute now, JS override removed)
    // The form with id="homeSearchForm" will now submit via its action attribute.
</script>
{% endblock %}
