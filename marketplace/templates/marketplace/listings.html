{% extends 'marketplace/base.html' %}

{% block title %}Toate anunțurile - Piata.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-4">
            <i class="fas fa-list mr-3 text-primary"></i>Toate anunțurile
        </h1>
        <p class="text-gray-600">Descoperă mii de anunțuri din toată România</p>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <!-- Search -->
            <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Caută anunțuri</label>
                <input type="text" name="search" value="{{ request.GET.search }}" 
                       placeholder="Ce cauți?" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            
            <!-- Category -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Categorie</label>
                <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="">Toate categoriile</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Price Range -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Preț minim</label>
                <input type="number" name="min_price" value="{{ request.GET.min_price }}" 
                       placeholder="0" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Preț maxim</label>
                <input type="number" name="max_price" value="{{ request.GET.max_price }}" 
                       placeholder="Orice preț" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            
            <!-- Location Search with Autocomplete -->
            <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-map-marker-alt mr-1"></i>Locație
                </label>
                <div class="relative">
                    <input type="text" 
                           name="location" 
                           id="location-search"
                           value="{{ request.GET.location }}" 
                           placeholder="Oraș, județ sau în apropierea mea..." 
                           class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                           autocomplete="off">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    
                    <!-- Use Current Location Button -->
                    <button type="button" 
                            onclick="useCurrentLocation()"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-primary hover:text-blue-700"
                            title="Folosește locația curentă">
                        <i class="fas fa-crosshairs"></i>
                    </button>
                    
                    <!-- Location Suggestions Dropdown -->
                    <div id="location-suggestions" 
                         class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg mt-1 hidden max-h-60 overflow-y-auto">
                    </div>
                </div>
                
                <!-- Distance Filter (shows when location is selected) -->
                <div id="distance-filter" class="mt-2 hidden">
                    <label class="block text-xs text-gray-600 mb-1">Raza de căutare</label>
                    <select name="radius" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-primary">
                        <option value="5" {% if request.GET.radius == '5' %}selected{% endif %}>5 km</option>
                        <option value="10" {% if request.GET.radius == '10' %}selected{% endif %}>10 km</option>
                        <option value="25" {% if request.GET.radius == '25' %}selected{% endif %}>25 km</option>
                        <option value="50" {% if request.GET.radius == '50' %}selected{% endif %}>50 km</option>
                        <option value="100" {% if request.GET.radius == '100' %}selected{% endif %}>100 km</option>
                    </select>
                </div>
            </div>
            
            <!-- Sort -->
            <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Sortează după</label>
                <select name="sort" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="-created_at" {% if request.GET.sort == '-created_at' %}selected{% endif %}>Cele mai recente</option>
                    <option value="created_at" {% if request.GET.sort == 'created_at' %}selected{% endif %}>Cele mai vechi</option>
                    <option value="price" {% if request.GET.sort == 'price' %}selected{% endif %}>Preț crescător</option>
                    <option value="-price" {% if request.GET.sort == '-price' %}selected{% endif %}>Preț descrescător</option>
                    <option value="title" {% if request.GET.sort == 'title' %}selected{% endif %}>Alfabetic</option>
                </select>
            </div>
            
            <!-- Filter Buttons -->
            <div class="flex space-x-4 items-end">
                <button type="submit" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>Caută
                </button>
                <a href="{% url 'marketplace:listings' %}" 
                   class="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-times mr-2"></i>Resetează
                </a>
            </div>
        </form>
    </div>

    <!-- Results Info -->
    <div class="flex items-center justify-between mb-6">
        <div class="text-gray-600">
            {% if listings.count > 0 %}
                <span class="font-semibold">{{ listings|length }}</span> din <span class="font-semibold">{{ listings.paginator.count }}</span> anunțuri
                {% if request.GET.search %}pentru "<strong>{{ request.GET.search }}</strong>"{% endif %}
            {% else %}
                Nu am găsit anunțuri
                {% if request.GET.search %}pentru "<strong>{{ request.GET.search }}</strong>"{% endif %}
            {% endif %}
        </div>
        
        <!-- View Toggle -->
        <div class="flex items-center space-x-2" x-data="{ view: 'grid' }">
            <button @click="view = 'grid'" 
                    :class="view === 'grid' ? 'bg-primary text-white' : 'border border-gray-300 text-gray-700'"
                    class="px-3 py-2 rounded-lg hover:bg-primary hover:text-white transition-colors">
                <i class="fas fa-th-large"></i>
            </button>
            <button @click="view = 'list'" 
                    :class="view === 'list' ? 'bg-primary text-white' : 'border border-gray-300 text-gray-700'"
                    class="px-3 py-2 rounded-lg hover:bg-primary hover:text-white transition-colors">
                <i class="fas fa-list"></i>
            </button>
        </div>
    </div>

    <!-- Listings Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" x-show="view === 'grid'">
        {% for listing in listings %}
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
            <a href="{% url 'marketplace:listing_detail' listing.id %}" class="block">
                <!-- Image -->
                <div class="h-48 bg-gray-200 relative">
                    {% if listing.main_image %}
                    <img src="{{ listing.main_image.image.url }}" alt="{{ listing.title }}" 
                         class="w-full h-full object-cover">
                    {% else %}
                    <div class="w-full h-full flex items-center justify-center text-gray-400">
                        <i class="fas fa-image text-4xl"></i>
                    </div>
                    {% endif %}
                    
                    <!-- Price Badge -->
                    <div class="absolute top-3 right-3 bg-black bg-opacity-75 text-white px-3 py-1 rounded-lg">
                        <span class="font-semibold">{{ listing.price }} RON</span>
                    </div>
                    
                    <!-- Category Badge -->
                    <div class="absolute top-3 left-3 bg-primary text-white px-2 py-1 rounded text-xs">
                        {{ listing.category.name }}
                    </div>
                </div>
                
                <!-- Content -->
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">{{ listing.title }}</h3>
                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ listing.description }}</p>
                    
                    <!-- Meta Info -->
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-1"></i>
                            <span>{{ listing.location }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-1"></i>
                            <span>{{ listing.created_at|timesince }}</span>
                        </div>
                    </div>
                    
                    <!-- User -->
                    <div class="flex items-center mt-3 pt-3 border-t border-gray-100">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-user text-sm text-gray-600"></i>
                        </div>
                        <span class="text-sm text-gray-600">{{ listing.user.username }}</span>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
    </div>

    <!-- Listings List View -->
    <div class="space-y-4" x-show="view === 'list'" style="display: none;">
        {% for listing in listings %}
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
            <a href="{% url 'marketplace:listing_detail' listing.id %}" class="block">
                <div class="flex">
                    <!-- Image -->
                    <div class="w-48 h-32 bg-gray-200 relative flex-shrink-0">
                        {% if listing.main_image %}
                        <img src="{{ listing.main_image.image.url }}" alt="{{ listing.title }}" 
                             class="w-full h-full object-cover">
                        {% else %}
                        <div class="w-full h-full flex items-center justify-center text-gray-400">
                            <i class="fas fa-image text-2xl"></i>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Content -->
                    <div class="flex-1 p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">{{ listing.title }}</h3>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-primary">{{ listing.price }} RON</div>
                                <div class="text-sm text-gray-500">{{ listing.category.name }}</div>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 mb-3 line-clamp-2">{{ listing.description }}</p>
                        
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <span>{{ listing.location }}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    <span>{{ listing.created_at|timesince }}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-user mr-1"></i>
                                    <span>{{ listing.user.username }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
    </div>

    <!-- No Results -->
    {% if not listings %}
    <div class="bg-white rounded-lg shadow-md p-12 text-center">
        <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">Nu am găsit anunțuri</h3>
        <p class="text-gray-500 mb-6">Încearcă să modifici criteriile de căutare sau să resetezi filtrele.</p>
        <div class="space-x-4">
            <a href="{% url 'marketplace:listings' %}" 
               class="inline-block border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-times mr-2"></i>Resetează filtrele
            </a>
            <a href="{% url 'marketplace:add_listing' %}" 
               class="inline-block bg-accent text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                <i class="fas fa-plus mr-2"></i>Adaugă primul anunț
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Pagination -->
    {% if listings.has_other_pages %}
    <div class="flex justify-center mt-12">
        <nav class="flex items-center space-x-2">
            {% if listings.has_previous %}
                <a href="?page={{ listings.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}{% if request.GET.location %}&location={{ request.GET.location }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}" 
                   class="px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    <i class="fas fa-chevron-left"></i>
                </a>
            {% endif %}
            
            {% for num in listings.paginator.page_range %}
                {% if num == listings.number %}
                    <span class="px-3 py-2 bg-primary text-white rounded-lg">{{ num }}</span>
                {% else %}
                    <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}{% if request.GET.location %}&location={{ request.GET.location }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}" 
                       class="px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">{{ num }}</a>
                {% endif %}
            {% endfor %}
            
            {% if listings.has_next %}
                <a href="?page={{ listings.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}{% if request.GET.location %}&location={{ request.GET.location }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}" 
                   class="px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    <i class="fas fa-chevron-right"></i>
                </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}
</div>

{% block extra_js %}
<script>
// Location search functionality
var locationTimeout;
const locationInput = document.getElementById('location-search');
const locationSuggestions = document.getElementById('location-suggestions');
const distanceFilter = document.getElementById('distance-filter');

// Show distance filter when location has value
if (locationInput && locationInput.value.trim()) {
    distanceFilter.classList.remove('hidden');
}

// Location search with autocomplete
if (locationInput) {
    locationInput.addEventListener('input', function() {
        const query = this.value.trim();
        
        if (query.length < 2) {
            locationSuggestions.classList.add('hidden');
            return;
        }
        
        // Clear existing timeout
        if (locationTimeout) {
            clearTimeout(locationTimeout);
        }
        
        // Debounce the search
        locationTimeout = setTimeout(() => {
            searchLocations(query);
        }, 300);
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!locationInput.contains(e.target) && !locationSuggestions.contains(e.target)) {
            locationSuggestions.classList.add('hidden');
        }
    });
}

function searchLocations(query) {
    fetch(`/api/locations/search/?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displayLocationSuggestions(data.results || []);
        })
        .catch(error => {
            console.error('Error searching locations:', error);
        });
}

function displayLocationSuggestions(suggestions) {
    if (suggestions.length === 0) {
        locationSuggestions.classList.add('hidden');
        return;
    }
    
    const html = suggestions.map(location => `
        <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-0"
             onclick="selectLocation('${location.display_name}', ${location.lat}, ${location.lon})">
            <div class="font-medium text-gray-800">${location.display_name}</div>
            ${location.type ? `<div class="text-xs text-gray-500">${location.type}</div>` : ''}
        </div>
    `).join('');
    
    locationSuggestions.innerHTML = html;
    locationSuggestions.classList.remove('hidden');
}

function selectLocation(displayName, lat, lon) {
    locationInput.value = displayName;
    locationSuggestions.classList.add('hidden');
    distanceFilter.classList.remove('hidden');
    
    // Store coordinates for potential use
    locationInput.dataset.lat = lat;
    locationInput.dataset.lon = lon;
}

function useCurrentLocation() {
    if (!navigator.geolocation) {
        alert('Locația nu este suportată de acest browser.');
        return;
    }
    
    // Show loading state
    const button = event.target.closest('button');
    const originalIcon = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;
    
    navigator.geolocation.getCurrentPosition(
        function(position) {
            const lat = position.coords.latitude;
            const lon = position.coords.longitude;
            
            // Reverse geocode to get address
            fetch(`/api/locations/reverse-geocode/?lat=${lat}&lon=${lon}`)
                .then(response => response.json())
                .then(data => {
                    if (data.address) {
                        locationInput.value = data.address;
                        locationInput.dataset.lat = lat;
                        locationInput.dataset.lon = lon;
                        distanceFilter.classList.remove('hidden');
                        
                        // Show success notification
                        showLocationNotification('Locația curentă a fost detectată cu succes!', 'success');
                    } else {
                        showLocationNotification('Nu s-a putut determina adresa pentru locația curentă.', 'error');
                    }
                })
                .catch(error => {
                    console.error('Reverse geocoding error:', error);
                    showLocationNotification('Eroare la determinarea adresei.', 'error');
                })
                .finally(() => {
                    // Restore button
                    button.innerHTML = originalIcon;
                    button.disabled = false;
                });
        },
        function(error) {
            let message = 'Nu s-a putut accesa locația.';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    message = 'Accesul la locație a fost refuzat.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    message = 'Locația nu este disponibilă.';
                    break;
                case error.TIMEOUT:
                    message = 'Cererea de locație a expirat.';
                    break;
            }
            showLocationNotification(message, 'error');
            
            // Restore button
            button.innerHTML = originalIcon;
            button.disabled = false;
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000
        }
    );
}

function showLocationNotification(message, type) {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-lg font-bold hover:opacity-70">×</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
{% endblock %}
