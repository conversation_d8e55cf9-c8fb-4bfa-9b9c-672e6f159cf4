{% extends 'marketplace/base.html' %}

{% block title %}Adaugă anunț - Piata.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-4">
            <i class="fas fa-plus mr-3 text-accent"></i>Adaugă un anunț nou
        </h1>
        <p class="text-gray-600">Completează formularul pentru a publica anunțul tău gratuit</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Form -->
        <div class="lg:col-span-3">
            <form method="POST" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}
                
                {% if form.errors %}
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Erori în formular
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    {% for field in form %}
                                        {% if field.errors %}
                                            {% for error in field.errors %}
                                                <li>{{ field.label }}: {{ error }}</li>
                                            {% endfor %}
                                        {% endif %}
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <!-- Image Upload Section -->
                    <div class="mb-6">
                        <label for="{{ form.images.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.images.label }} {% if form.images.field.required %}<span class="text-red-500">*</span>{% endif %}
                        </label>
                        
                        <!-- Image Preview Area -->
                        <div id="image-preview-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4">
                            <!-- Preview images will be inserted here -->
                        </div>
                        
                        <!-- Upload Area -->
                        <div class="flex items-center justify-center w-full">
                            <label for="id_images" class="w-full flex flex-col items-center px-4 py-6 bg-white rounded-lg border-2 border-dashed border-gray-300 cursor-pointer hover:bg-gray-50 transition-colors">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-3"></i>
                                    <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">Click pentru a încărca</span> sau trage imaginile aici</p>
                                    <p class="text-xs text-gray-500">PNG, JPG, JPEG până la 5MB (maxim 10 imagini)</p>
                                </div>
                                {{ form.images }}
                            </label>
                        </div>
                        
                        {% if form.images.help_text %}
                            <p class="text-xs text-gray-500 mt-1">{{ form.images.help_text }}</p>
                        {% endif %}
                        
                        {% if form.images.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.images.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <h2 class="text-xl font-semibold text-gray-800 mb-6">
                        <i class="fas fa-info-circle mr-2 text-primary"></i>Informații de bază
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Title -->
                        <div class="md:col-span-2">
                            <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.title.label }} {% if form.title.field.required %}<span class="text-red-500">*</span>{% endif %}
                            </label>
                            {{ form.title }}
                            <p class="text-xs text-gray-500 mt-1">Alege un titlu descriptiv și atractiv</p>
                            {% if form.title.errors %}
                                <p class="text-red-500 text-xs mt-1">{{ form.title.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Category -->
                        <div>
                            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.category.label }} {% if form.category.field.required %}<span class="text-red-500">*</span>{% endif %}
                            </label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <p class="text-red-500 text-xs mt-1">{{ form.category.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Subcategory -->
                        <div>
                            <label for="{{ form.subcategory.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.subcategory.label }}
                            </label>
                            {{ form.subcategory }}
                            {% if form.subcategory.errors %}
                                <p class="text-red-500 text-xs mt-1">{{ form.subcategory.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Price & Currency -->
                        <div>
                            <label for="{{ form.price.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.price.label }} {% if form.price.field.required %}<span class="text-red-500">*</span>{% endif %}
                            </label>
                            <div class="flex space-x-2">
                                <div class="flex-1">
                                    {{ form.price }}
                                </div>
                                <div class="w-1/3">
                                    {{ form.currency }}
                                </div>
                            </div>
                            {% if form.price.errors %}
                                <p class="text-red-500 text-xs mt-1">{{ form.price.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Location Fields -->
                        <div class="md:col-span-2">
                            <h3 class="text-lg font-medium text-gray-800 mb-4">
                                <i class="fas fa-map-marker-alt mr-2 text-primary"></i>Locația produsului
                            </h3>
                            
                            <!-- Use Current Location Checkbox -->
                            <div class="mb-4">
                                <label class="flex items-center">
                                    {{ form.use_current_location }}
                                    <span class="ml-2 text-sm text-gray-700">{{ form.use_current_location.label }}</span>
                                </label>
                                <p class="text-xs text-gray-500 mt-1">Permite accesul la locație pentru completarea automată</p>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- City -->
                                <div>
                                    <label for="{{ form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ form.city.label }} {% if form.city.field.required %}<span class="text-red-500">*</span>{% endif %}
                                    </label>
                                    {{ form.city }}
                                    {% if form.city.help_text %}
                                        <p class="text-xs text-gray-500 mt-1">{{ form.city.help_text }}</p>
                                    {% endif %}
                                    {% if form.city.errors %}
                                        <p class="text-red-500 text-xs mt-1">{{ form.city.errors.0 }}</p>
                                    {% endif %}
                                    
                                    <!-- City suggestions datalist -->
                                    <datalist id="city-datalist">
                                        <option value="București">
                                        <option value="Cluj-Napoca">
                                        <option value="Timișoara">
                                        <option value="Iași">
                                        <option value="Constanța">
                                        <option value="Craiova">
                                        <option value="Brașov">
                                        <option value="Galați">
                                        <option value="Ploiești">
                                        <option value="Oradea">
                                        <option value="Brăila">
                                        <option value="Arad">
                                        <option value="Pitești">
                                        <option value="Sibiu">
                                        <option value="Bacău">
                                        <option value="Târgu Mureș">
                                        <option value="Baia Mare">
                                        <option value="Buzău">
                                        <option value="Botoșani">
                                        <option value="Satu Mare">
                                    </datalist>
                                </div>
                                
                                <!-- County -->
                                <div>
                                    <label for="{{ form.county.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ form.county.label }}
                                    </label>
                                    {{ form.county }}
                                    {% if form.county.errors %}
                                        <p class="text-red-500 text-xs mt-1">{{ form.county.errors.0 }}</p>
                                    {% endif %}
                                </div>
                                
                                <!-- Address -->
                                <div class="md:col-span-2">
                                    <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ form.address.label }}
                                    </label>
                                    {{ form.address }}
                                    {% if form.address.help_text %}
                                        <p class="text-xs text-gray-500 mt-1">{{ form.address.help_text }}</p>
                                    {% endif %}
                                    {% if form.address.errors %}
                                        <p class="text-red-500 text-xs mt-1">{{ form.address.errors.0 }}</p>
                                    {% endif %}
                                </div>
                                
                                <!-- Postal Code -->
                                <div>
                                    <label for="{{ form.postal_code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ form.postal_code.label }}
                                    </label>
                                    {{ form.postal_code }}
                                    {% if form.postal_code.errors %}
                                        <p class="text-red-500 text-xs mt-1">{{ form.postal_code.errors.0 }}</p>
                                    {% endif %}
                                </div>
                                
                                <!-- Auto-filled location field (hidden from users, but shows computed location) -->
                                <div>
                                    <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ form.location.label }}
                                    </label>
                                    {{ form.location }}
                                    {% if form.location.help_text %}
                                        <p class="text-xs text-gray-500 mt-1">{{ form.location.help_text }}</p>
                                    {% endif %}
                                    {% if form.location.errors %}
                                        <p class="text-red-500 text-xs mt-1">{{ form.location.errors.0 }}</p>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Hidden coordinate fields -->
                            {{ form.latitude }}
                            {{ form.longitude }}
                            
                            <!-- Location status indicator -->
                            <div id="location-status" class="mt-4 hidden">
                                <div class="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span class="text-green-700 text-sm">Locația a fost identificată cu succes</span>
                                </div>
                            </div>
                            
                            <div id="location-error" class="mt-4 hidden">
                                <div class="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                                    <span class="text-yellow-700 text-sm">Nu s-a putut identifica locația exactă. Te rog verifică datele introduse.</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-6">
                        <i class="fas fa-align-left mr-2 text-primary"></i>Descriere
                    </h2>
                    
                    <div>
                        <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.description.label }} {% if form.description.field.required %}<span class="text-red-500">*</span>{% endif %}
                        </label>
                        {{ form.description }}
                        <p class="text-xs text-gray-500 mt-1">O descriere detaliată crește șansele de vânzare</p>
                        {% if form.description.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.description.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Images -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-6">
                        <i class="fas fa-camera mr-2 text-primary"></i>Imagini
                    </h2>
                    
                    <div class="space-y-4">
                        <!-- Main Image -->
                        <div>
                            <label for="{{ form.image.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Imagine principală
                            </label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <div id="image-preview" class="hidden">
                                    <img id="preview-img" class="max-h-48 mx-auto rounded">
                                    <button type="button" onclick="removeImage()" class="mt-2 text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash mr-1"></i>Elimină
                                    </button>
                                </div>
                                <div id="image-placeholder">
                                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                    <p class="text-gray-600 mb-2">Apasă pentru a încărca o imagine</p>
                                    <p class="text-xs text-gray-500">PNG, JPG până la 10MB</p>
                                    {{ form.image }}
                                    <button type="button" onclick="document.getElementById('{{ form.image.id_for_label }}').click()" 
                                            class="mt-4 bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                        Selectează imagine
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="font-semibold text-blue-800 mb-2">
                                <i class="fas fa-lightbulb mr-2"></i>Sfaturi pentru imagini bune
                            </h4>
                            <ul class="text-blue-700 text-sm space-y-1">
                                <li><i class="fas fa-check mr-2"></i>Folosește lumină naturală</li>
                                <li><i class="fas fa-check mr-2"></i>Fotografiază din mai multe unghiuri</li>
                                <li><i class="fas fa-check mr-2"></i>Arată eventualele defecte</li>
                                <li><i class="fas fa-check mr-2"></i>Evită reflexiile și umbrele</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-6">
                        <i class="fas fa-phone mr-2 text-primary"></i>Informații de contact
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="contact_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Nume de contact
                            </label>
                            <input type="text" id="contact_name" name="contact_name" value="{{ user.get_full_name|default:user.username }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="Numele tău complet">
                        </div>
                        
                        <div>
                            <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                Telefon
                            </label>
                            <input type="tel" id="contact_phone" name="contact_phone"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="+40 7XX XXX XXX">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email
                            </label>
                            <input type="email" id="contact_email" name="contact_email" value="{{ user.email }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                </div>

                <!-- Submit -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" required class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">
                                    Accept <a href="#" class="text-primary hover:underline">termenii și condițiile</a> și 
                                    <a href="#" class="text-primary hover:underline">politica de confidențialitate</a>
                                </span>
                            </label>
                        </div>
                        
                        <div class="flex space-x-4">
                            <a href="{% url 'marketplace:home' %}" 
                               class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors">
                                Anulează
                            </a>
                            <button type="submit" 
                                    class="bg-accent text-white px-8 py-3 rounded-lg hover:bg-green-600 transition-colors">
                                <i class="fas fa-plus mr-2"></i>Publică anunțul
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Preview -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6 sticky top-24">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-eye mr-2"></i>Previzualizare
                </h3>
                
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <div class="h-32 bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-image text-2xl text-gray-400"></i>
                    </div>
                    <div class="p-3">
                        <h4 class="font-semibold text-gray-800 mb-1" id="preview-title">Titlu anunț</h4>
                        <div class="text-lg font-bold text-primary mb-2" id="preview-price">0 RON</div>
                        <p class="text-xs text-gray-500 flex items-center">
                            <i class="fas fa-map-marker-alt mr-1"></i>
                            <span id="preview-location">Locație</span>
                        </p>
                    </div>
                </div>
                
                <div class="mt-4 text-sm text-gray-600">
                    <p><i class="fas fa-info-circle mr-2"></i>Astfel va arăta anunțul tău în listă</p>
                </div>
            </div>

            <!-- Tips -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-green-800 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>Sfaturi pentru succes
                </h3>
                <ul class="text-green-700 text-sm space-y-2">
                    <li><i class="fas fa-check mr-2"></i>Adaugă cât mai multe detalii</li>
                    <li><i class="fas fa-check mr-2"></i>Folosește imagini de calitate</li>
                    <li><i class="fas fa-check mr-2"></i>Specifică starea produsului</li>
                    <li><i class="fas fa-check mr-2"></i>Menționează motivul vânzării</li>
                    <li><i class="fas fa-check mr-2"></i>Actualizează anunțul regulat</li>
                </ul>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Location service functionality
    const useCurrentLocationCheckbox = document.getElementById('{{ form.use_current_location.id_for_label }}');
    const cityInput = document.getElementById('{{ form.city.id_for_label }}');
    const countyInput = document.getElementById('{{ form.county.id_for_label }}');
    const addressInput = document.getElementById('{{ form.address.id_for_label }}');
    const locationInput = document.getElementById('{{ form.location.id_for_label }}');
    const latitudeInput = document.getElementById('{{ form.latitude.id_for_label }}');
    const longitudeInput = document.getElementById('{{ form.longitude.id_for_label }}');
    const locationStatus = document.getElementById('location-status');
    const locationError = document.getElementById('location-error');
    
    // Get current location
    useCurrentLocationCheckbox.addEventListener('change', function() {
        if (this.checked) {
            getCurrentLocation();
        }
    });
    
    function getCurrentLocation() {
        if ('geolocation' in navigator) {
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    
                    latitudeInput.value = lat;
                    longitudeInput.value = lng;
                    
                    // Reverse geocode to get address
                    reverseGeocode(lat, lng);
                },
                function(error) {
                    console.error('Geolocation error:', error);
                    showLocationError('Nu s-a putut accesa locația curentă. Te rog să introduci manual adresa.');
                    useCurrentLocationCheckbox.checked = false;
                }
            );
        } else {
            showLocationError('Browserul nu suportă geolocația. Te rog să introduci manual adresa.');
            useCurrentLocationCheckbox.checked = false;
        }
    }
    
    function reverseGeocode(lat, lng) {
        fetch(`/api/location/reverse-geocode/?lat=${lat}&lng=${lng}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.city) cityInput.value = data.city;
                    if (data.county) countyInput.value = data.county;
                    if (data.address) addressInput.value = data.address;
                    updateLocationDisplay();
                    showLocationStatus();
                } else {
                    showLocationError('Nu s-a putut identifica adresa pentru locația curentă.');
                }
            })
            .catch(error => {
                console.error('Reverse geocoding error:', error);
                showLocationError('Eroare la identificarea adresei.');
            });
    }
    
    // Geocode when city or address changes
    let geocodeTimeout;
    function debounceGeocode() {
        clearTimeout(geocodeTimeout);
        geocodeTimeout = setTimeout(geocodeAddress, 1000);
    }
    
    cityInput.addEventListener('input', debounceGeocode);
    addressInput.addEventListener('input', debounceGeocode);
    
    function geocodeAddress() {
        const city = cityInput.value.trim();
        const address = addressInput.value.trim();
        
        if (!city && !address) return;
        
        const query = address ? `${address}, ${city}` : city;
        
        fetch(`/api/location/geocode/?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.results.length > 0) {
                    const result = data.results[0];
                    latitudeInput.value = result.latitude;
                    longitudeInput.value = result.longitude;
                    
                    // Fill in missing data
                    if (!cityInput.value && result.city) {
                        cityInput.value = result.city;
                    }
                    if (!countyInput.value && result.county) {
                        countyInput.value = result.county;
                    }
                    
                    updateLocationDisplay();
                    showLocationStatus();
                } else {
                    hideLocationStatus();
                }
            })
            .catch(error => {
                console.error('Geocoding error:', error);
                hideLocationStatus();
            });
    }
    
    function updateLocationDisplay() {
        const city = cityInput.value.trim();
        const county = countyInput.value.trim();
        const parts = [];
        
        if (city) parts.push(city);
        if (county) parts.push(county);
        
        locationInput.value = parts.join(', ');
        
        // Update preview
        const previewLocation = document.getElementById('preview-location');
        if (previewLocation) {
            previewLocation.textContent = locationInput.value || 'Locație';
        }
    }
    
    function showLocationStatus() {
        locationStatus.classList.remove('hidden');
        locationError.classList.add('hidden');
    }
    
    function showLocationError(message) {
        if (message) {
            locationError.querySelector('span').textContent = message;
        }
        locationError.classList.remove('hidden');
        locationStatus.classList.add('hidden');
    }
    
    function hideLocationStatus() {
        locationStatus.classList.add('hidden');
        locationError.classList.add('hidden');
    }
    
    // Update location display when city or county changes
    cityInput.addEventListener('input', updateLocationDisplay);
    countyInput.addEventListener('input', updateLocationDisplay);
    
    // Romanian counties for auto-completion
    const romanianCounties = [
        'Alba', 'Arad', 'Argeș', 'Bacău', 'Bihor', 'Bistrița-Năsăud', 'Botoșani', 'Brașov',
        'Brăila', 'București', 'Buzău', 'Caraș-Severin', 'Călărași', 'Cluj', 'Constanța',
        'Covasna', 'Dâmbovița', 'Dolj', 'Galați', 'Giurgiu', 'Gorj', 'Harghita', 'Hunedoara',
        'Ialomița', 'Iași', 'Ilfov', 'Maramureș', 'Mehedinți', 'Mureș', 'Neamț', 'Olt',
        'Prahova', 'Satu Mare', 'Sălaj', 'Sibiu', 'Suceava', 'Teleorman', 'Timiș', 'Tulcea',
        'Vaslui', 'Vâlcea', 'Vrancea'
    ];
    
    // Auto-complete for county
    let countyDatalist = document.createElement('datalist');
    countyDatalist.id = 'county-datalist';
    romanianCounties.forEach(county => {
        let option = document.createElement('option');
        option.value = county;
        countyDatalist.appendChild(option);
    });
    document.body.appendChild(countyDatalist);
    countyInput.setAttribute('list', 'county-datalist');
    
    // Image preview functionality
    const imagesInput = document.getElementById('{{ form.images.id_for_label }}');
    const imagePreviewGrid = document.getElementById('image-preview-grid');
    
    if (imagesInput) {
        imagesInput.addEventListener('change', function(e) {
            imagePreviewGrid.innerHTML = '';
            const files = Array.from(e.target.files);
            
            files.forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewDiv = document.createElement('div');
                        previewDiv.className = 'relative group';
                        previewDiv.innerHTML = `
                            <img src="${e.target.result}" class="w-full h-24 object-cover rounded-lg border">
                            <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-lg">
                                <button type="button" onclick="removeImage(${index})" class="text-white hover:text-red-300">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            ${index === 0 ? '<div class="absolute top-1 left-1 bg-primary text-white text-xs px-1 rounded">Principală</div>' : ''}
                        `;
                        imagePreviewGrid.appendChild(previewDiv);
                    };
                    reader.readAsDataURL(file);
                }
            });
        });
    }
    
    // Live preview updates
    const titleInput = document.getElementById('{{ form.title.id_for_label }}');
    const priceInput = document.getElementById('{{ form.price.id_for_label }}');
    const currencySelect = document.getElementById('{{ form.currency.id_for_label }}');
    
    const previewTitle = document.getElementById('preview-title');
    const previewPrice = document.getElementById('preview-price');
    const previewLocation = document.getElementById('preview-location');
    
    if (titleInput) {
        titleInput.addEventListener('input', function() {
            previewTitle.textContent = this.value || 'Titlu anunț';
        });
    }
    
    function updatePreviewPrice() {
        const price = priceInput.value;
        const currency = currencySelect.value || 'RON';
        previewPrice.textContent = price ? `${price} ${currency}` : '0 RON';
    }
    
    if (priceInput) {
        priceInput.addEventListener('input', updatePreviewPrice);
    }
    if (currencySelect) {
        currencySelect.addEventListener('change', updatePreviewPrice);
    }
    
    // Dynamic subcategory loading
    const categorySelect = document.getElementById('{{ form.category.id_for_label }}');
    const subcategorySelect = document.getElementById('{{ form.subcategory.id_for_label }}');
    
    if (categorySelect && subcategorySelect) {
        categorySelect.addEventListener('change', function() {
            const categoryId = this.value;
            
            // Clear subcategory options
            subcategorySelect.innerHTML = '<option value="">Selectează subcategoria</option>';
            
            if (categoryId) {
                // Fetch subcategories via AJAX
                fetch(`/api/categories/${categoryId}/subcategories/`)
                    .then(response => response.json())
                    .then(data => {
                        data.forEach(subcategory => {
                            const option = document.createElement('option');
                            option.value = subcategory.id;
                            option.textContent = subcategory.name;
                            subcategorySelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error fetching subcategories:', error));
            }
        });
    }
});

// Remove image function (global scope for onclick)
function removeImage(index) {
    const imagesInput = document.getElementById('{{ form.images.id_for_label }}');
    const dt = new DataTransfer();
    const files = Array.from(imagesInput.files);
    
    files.forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });
    
    <!-- Load image preview functionality -->
    {% load static %}
    <script src="{% static 'js/image-preview.js' %}"></script>

    {% endblock %}
    {% endblock %}
