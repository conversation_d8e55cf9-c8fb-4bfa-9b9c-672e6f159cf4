{% extends 'marketplace/base.html' %}

{% block title %}Mesaje - Piata.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 h-96">
        <!-- Messages List -->
        <div class="lg:col-span-1 bg-white rounded-lg shadow-md">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">
                    <i class="fas fa-envelope mr-2"></i>Conversații
                </h2>
            </div>
            
            <div class="overflow-y-auto h-80">
                {% if conversations %}
                    {% for conversation in conversations %}
                    <a href="{% url 'marketplace:conversation' conversation.other_user.id %}" 
                       class="block p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-gray-600"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-gray-800">{{ conversation.other_user.username }}</h3>
                                    <span class="text-xs text-gray-500">{{ conversation.last_message.created_at|timesince }}</span>
                                </div>
                                <p class="text-sm text-gray-600 truncate">{{ conversation.last_message.content|truncatewords:8 }}</p>
                                {% if conversation.unread_count %}
                                <span class="inline-block bg-primary text-white text-xs px-2 py-1 rounded-full mt-1">
                                    {{ conversation.unread_count }}
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </a>
                    {% endfor %}
                {% else %}
                <div class="p-8 text-center text-gray-500">
                    <i class="fas fa-envelope-open text-4xl mb-4"></i>
                    <p>Nu ai conversații încă</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Chat Area -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow-md flex flex-col">
            <div id="chat-header" class="p-4 border-b border-gray-200 hidden">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-gray-600"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800" id="chat-user-name"></h3>
                            <p class="text-sm text-gray-500">Online acum 5 min</p>
                        </div>
                    </div>
                    <button class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <div id="chat-messages" class="flex-1 p-4 overflow-y-auto">
                <div id="no-chat-selected" class="h-full flex items-center justify-center text-gray-500">
                    <div class="text-center">
                        <i class="fas fa-comments text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">Selectează o conversație</h3>
                        <p>Alege o conversație din stânga pentru a vedea mesajele</p>
                    </div>
                </div>
                
                <div id="messages-container" class="space-y-4 hidden">
                    <!-- Messages will be loaded here -->
                </div>
            </div>

            <div id="chat-input" class="p-4 border-t border-gray-200 hidden">
                <form id="message-form" class="flex space-x-4">
                    {% csrf_token %}
                    <input type="hidden" id="conversation-id" name="conversation_id">
                    <input type="text" id="message-input" name="content" required
                           placeholder="Scrie un mesaj..."
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    <button type="submit" 
                            class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-800 mb-4">
            <i class="fas fa-info-circle mr-2"></i>Sfaturi pentru mesaje sigure
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-700 text-sm">
            <ul class="space-y-2">
                <li><i class="fas fa-shield-alt mr-2"></i>Nu împărtăși informații personale</li>
                <li><i class="fas fa-eye mr-2"></i>Întâlnește-te în locuri publice</li>
            </ul>
            <ul class="space-y-2">
                <li><i class="fas fa-credit-card mr-2"></i>Nu trimite bani în avans</li>
                <li><i class="fas fa-exclamation-triangle mr-2"></i>Raportează comportamentul suspect</li>
            </ul>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
let currentConversationId = null;

function loadConversation(conversationId) {
    currentConversationId = conversationId;
    
    // Show chat interface
    document.getElementById('no-chat-selected').classList.add('hidden');
    document.getElementById('chat-header').classList.remove('hidden');
    document.getElementById('chat-input').classList.remove('hidden');
    document.getElementById('messages-container').classList.remove('hidden');
    
    // Update conversation ID in form
    document.getElementById('conversation-id').value = conversationId;
    
    // Load messages (this would be an AJAX call in a real implementation)
    loadMessages(conversationId);
}

function loadMessages(conversationId) {
    // This would be an AJAX call to load messages
    // For now, we'll show a placeholder
    const messagesContainer = document.getElementById('messages-container');
    messagesContainer.innerHTML = `
        <div class="text-center text-gray-500 py-8">
            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
            <p>Se încarcă mesajele...</p>
        </div>
    `;
    
    // Simulate loading messages
    setTimeout(() => {
        messagesContainer.innerHTML = `
            <div class="flex justify-start mb-4">
                <div class="bg-gray-100 rounded-lg p-3 max-w-xs">
                    <p class="text-gray-800">Salut! Este încă disponibil produsul?</p>
                    <span class="text-xs text-gray-500">acum 2 ore</span>
                </div>
            </div>
            <div class="flex justify-end mb-4">
                <div class="bg-primary text-white rounded-lg p-3 max-w-xs">
                    <p>Da, este disponibil. Vrei să ne întâlnim să îl vezi?</p>
                    <span class="text-xs text-blue-200">acum 1 oră</span>
                </div>
            </div>
            <div class="flex justify-start mb-4">
                <div class="bg-gray-100 rounded-lg p-3 max-w-xs">
                    <p class="text-gray-800">Perfect! Când ai timp?</p>
                    <span class="text-xs text-gray-500">acum 30 min</span>
                </div>
            </div>
        `;
    }, 1000);
}

// Handle message form submission
document.getElementById('message-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const messageInput = document.getElementById('message-input');
    const message = messageInput.value.trim();
    
    if (message && currentConversationId) {
        // Add message to chat (this would be an AJAX call in real implementation)
        const messagesContainer = document.getElementById('messages-container');
        messagesContainer.innerHTML += `
            <div class="flex justify-end mb-4">
                <div class="bg-primary text-white rounded-lg p-3 max-w-xs">
                    <p>${message}</p>
                    <span class="text-xs text-blue-200">acum</span>
                </div>
            </div>
        `;
        
        // Clear input
        messageInput.value = '';
        
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
});
</script>
{% endblock %}
{% endblock %}
