{% extends 'marketplace/base.html' %}

{% block title %}Căutare{% if query %} - {{ query }}{% endif %} - Piata.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Search Header -->
    <div class="mb-8">
        {% if query %}
        <h1 class="text-3xl font-bold text-gray-800 mb-4">
            <i class="fas fa-search mr-3 text-primary"></i>Rezultate pentru "{{ query }}"
        </h1>
        <p class="text-gray-600">
            {% if total_results > 0 %}
                Am găsit {{ total_results }} anunțuri care se potrivesc căutării tale
            {% else %}
                Nu am găsit anunțuri pentru căutarea ta
            {% endif %}
        </p>
        {% else %}
        <h1 class="text-3xl font-bold text-gray-800 mb-4">
            <i class="fas fa-search mr-3 text-primary"></i><PERSON><PERSON><PERSON> anunțuri
        </h1>
        <p class="text-gray-600">Găsește exact ce cauți din mii de anunțuri</p>
        {% endif %}
    </div>

    <!-- Enhanced Search Form -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <form method="GET" class="space-y-6">
            <!-- Main Search -->
            <div>
                <label for="q" class="block text-sm font-medium text-gray-700 mb-2">Ce cauți?</label>
                <div class="relative">
                    <input type="text" id="q" name="q" value="{{ query }}" 
                           placeholder="Caută anunțuri..."
                           class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Filters Row -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Categorie</label>
                    <select id="category" name="category" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Toate categoriile</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Location -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Locație</label>
                    <div class="relative">
                        <input type="text" id="location" name="location" value="{{ request.GET.location }}"
                               placeholder="Oraș, județ..."
                               class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-map-marker-alt text-gray-400"></i>
                        </div>
                        <button type="button" id="use-current-location" 
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-primary hover:text-blue-700">
                            <i class="fas fa-crosshairs"></i>
                        </button>
                    </div>
                    <div class="mt-2">
                        <label class="flex items-center text-sm text-gray-600">
                            <input type="checkbox" id="enable-distance" name="enable_distance" 
                                   {% if request.GET.enable_distance %}checked{% endif %}
                                   class="rounded border-gray-300 text-primary focus:ring-primary mr-2">
                            Caută în raza de 
                            <select name="distance" class="ml-1 mr-1 border-0 bg-transparent focus:ring-0 text-primary">
                                <option value="5" {% if request.GET.distance == "5" %}selected{% endif %}>5</option>
                                <option value="10" {% if request.GET.distance == "10" or not request.GET.distance %}selected{% endif %}>10</option>
                                <option value="25" {% if request.GET.distance == "25" %}selected{% endif %}>25</option>
                                <option value="50" {% if request.GET.distance == "50" %}selected{% endif %}>50</option>
                                <option value="100" {% if request.GET.distance == "100" %}selected{% endif %}>100</option>
                            </select>
                            km
                        </label>
                    </div>
                </div>

                <!-- Price Min -->
                <div>
                    <label for="min_price" class="block text-sm font-medium text-gray-700 mb-2">Preț de la</label>
                    <input type="number" id="min_price" name="min_price" value="{{ request.GET.min_price }}"
                           placeholder="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>

                <!-- Price Max -->
                <div>
                    <label for="max_price" class="block text-sm font-medium text-gray-700 mb-2">Preț până la</label>
                    <input type="number" id="max_price" name="max_price" value="{{ request.GET.max_price }}"
                           placeholder="Orice preț"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>
            </div>

            <!-- Advanced Filters -->
            <div class="border-t border-gray-200 pt-6" x-data="{ showAdvanced: false }">
                <button type="button" @click="showAdvanced = !showAdvanced" 
                        class="flex items-center text-primary hover:text-blue-700 transition-colors">
                    <span>Filtre avansate</span>
                    <i class="fas fa-chevron-down ml-2 transition-transform" :class="showAdvanced ? 'rotate-180' : ''"></i>
                </button>

                <div x-show="showAdvanced" x-transition class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Sort -->
                    <div>
                        <label for="sort" class="block text-sm font-medium text-gray-700 mb-2">Sortează după</label>
                        <select id="sort" name="sort"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="-created_at" {% if request.GET.sort == '-created_at' %}selected{% endif %}>Cele mai recente</option>
                            <option value="created_at" {% if request.GET.sort == 'created_at' %}selected{% endif %}>Cele mai vechi</option>
                            <option value="price" {% if request.GET.sort == 'price' %}selected{% endif %}>Preț crescător</option>
                            <option value="-price" {% if request.GET.sort == '-price' %}selected{% endif %}>Preț descrescător</option>
                            <option value="title" {% if request.GET.sort == 'title' %}selected{% endif %}>Alfabetic</option>
                            <option value="distance" {% if request.GET.sort == 'distance' %}selected{% endif %}>Distanță (aproape)</option>
                        </select>
                    </div>

                    <!-- Date Range -->
                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">Publicat după</label>
                        <input type="date" id="date_from" name="date_from" value="{{ request.GET.date_from }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>

                    <!-- Condition -->
                    <div>
                        <label for="condition" class="block text-sm font-medium text-gray-700 mb-2">Starea</label>
                        <select id="condition" name="condition"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Orice stare</option>
                            <option value="new" {% if request.GET.condition == 'new' %}selected{% endif %}>Nou</option>
                            <option value="excellent" {% if request.GET.condition == 'excellent' %}selected{% endif %}>Excelent</option>
                            <option value="good" {% if request.GET.condition == 'good' %}selected{% endif %}>Bună</option>
                            <option value="fair" {% if request.GET.condition == 'fair' %}selected{% endif %}>Acceptabilă</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Search Buttons -->
            <div class="flex space-x-4">
                <button type="submit" 
                        class="bg-primary text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>Caută
                </button>
                <a href="{% url 'marketplace:search' %}" 
                   class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-times mr-2"></i>Resetează
                </a>
            </div>
        </form>
    </div>

    {% if query %}
    <!-- Search Results -->
    <div class="flex items-center justify-between mb-6">
        <div class="text-gray-600">
            {% if results.count > 0 %}
                Afișez <span class="font-semibold">{{ results|length }}</span> din 
                <span class="font-semibold">{{ results.paginator.count }}</span> rezultate
            {% else %}
                <span class="font-semibold">0</span> rezultate găsite
            {% endif %}
        </div>
        
        {% if results.count > 0 %}
        <!-- View Toggle -->
        <div class="flex items-center space-x-2" x-data="{ view: 'grid' }">
            <button @click="view = 'grid'" 
                    :class="view === 'grid' ? 'bg-primary text-white' : 'border border-gray-300 text-gray-700'"
                    class="px-3 py-2 rounded-lg hover:bg-primary hover:text-white transition-colors">
                <i class="fas fa-th-large"></i>
            </button>
            <button @click="view = 'list'" 
                    :class="view === 'list' ? 'bg-primary text-white' : 'border border-gray-300 text-gray-700'"
                    class="px-3 py-2 rounded-lg hover:bg-primary hover:text-white transition-colors">
                <i class="fas fa-list"></i>
            </button>
        </div>
        {% endif %}
    </div>

    {% if results %}
    <!-- Results Grid View -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" x-show="view === 'grid'">
        {% for listing in results %}
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
            <a href="{% url 'marketplace:listing_detail' listing.id %}" class="block">
                <!-- Image -->
                <div class="h-48 bg-gray-200 relative">
                    {% if listing.image %}
                    <img src="{{ listing.image.url }}" alt="{{ listing.title }}" 
                         class="w-full h-full object-cover">
                    {% else %}
                    <div class="w-full h-full flex items-center justify-center text-gray-400">
                        <i class="fas fa-image text-4xl"></i>
                    </div>
                    {% endif %}
                    
                    <!-- Price Badge -->
                    <div class="absolute top-3 right-3 bg-black bg-opacity-75 text-white px-3 py-1 rounded-lg">
                        <span class="font-semibold">{{ listing.price }} RON</span>
                    </div>
                    
                    <!-- Category Badge -->
                    <div class="absolute top-3 left-3 bg-primary text-white px-2 py-1 rounded text-xs">
                        {{ listing.category.name }}
                    </div>
                </div>
                
                <!-- Content -->
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">{{ listing.title }}</h3>
                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ listing.description }}</p>
                    
                    <!-- Meta Info -->
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-1"></i>
                            <span>{{ listing.location }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-1"></i>
                            <span>{{ listing.created_at|timesince }}</span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
    </div>

    <!-- Results List View -->
    <div class="space-y-4" x-show="view === 'list'" style="display: none;">
        {% for listing in results %}
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
            <a href="{% url 'marketplace:listing_detail' listing.id %}" class="block">
                <div class="flex">
                    <!-- Image -->
                    <div class="w-48 h-32 bg-gray-200 relative flex-shrink-0">
                        {% if listing.image %}
                        <img src="{{ listing.image.url }}" alt="{{ listing.title }}" 
                             class="w-full h-full object-cover">
                        {% else %}
                        <div class="w-full h-full flex items-center justify-center text-gray-400">
                            <i class="fas fa-image text-2xl"></i>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Content -->
                    <div class="flex-1 p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">{{ listing.title }}</h3>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-primary">{{ listing.price }} RON</div>
                                <div class="text-sm text-gray-500">{{ listing.category.name }}</div>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 mb-3 line-clamp-2">{{ listing.description }}</p>
                        
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <span>{{ listing.location }}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    <span>{{ listing.created_at|timesince }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if results.has_other_pages %}
    <div class="flex justify-center mt-12">
        <nav class="flex items-center space-x-2">
            {% if results.has_previous %}
                <a href="?page={{ results.previous_page_number }}&q={{ query }}{% for key, value in request.GET.items %}{% if key != 'page' and key != 'q' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                   class="px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    <i class="fas fa-chevron-left"></i>
                </a>
            {% endif %}
            
            {% for num in results.paginator.page_range %}
                {% if num == results.number %}
                    <span class="px-3 py-2 bg-primary text-white rounded-lg">{{ num }}</span>
                {% else %}
                    <a href="?page={{ num }}&q={{ query }}{% for key, value in request.GET.items %}{% if key != 'page' and key != 'q' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                       class="px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">{{ num }}</a>
                {% endif %}
            {% endfor %}
            
            {% if results.has_next %}
                <a href="?page={{ results.next_page_number }}&q={{ query }}{% for key, value in request.GET.items %}{% if key != 'page' and key != 'q' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                   class="px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    <i class="fas fa-chevron-right"></i>
                </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- No Results -->
    <div class="bg-white rounded-lg shadow-md p-12 text-center">
        <i class="fas fa-search-minus text-6xl text-gray-300 mb-6"></i>
        <h3 class="text-2xl font-semibold text-gray-600 mb-4">Nu am găsit rezultate</h3>
        <div class="max-w-md mx-auto text-gray-500 mb-8">
            <p class="mb-4">Încearcă să:</p>
            <ul class="text-left space-y-2">
                <li><i class="fas fa-check text-green-500 mr-2"></i>Verifici ortografia</li>
                <li><i class="fas fa-check text-green-500 mr-2"></i>Folosești termeni mai generali</li>
                <li><i class="fas fa-check text-green-500 mr-2"></i>Încerci sinonime</li>
                <li><i class="fas fa-check text-green-500 mr-2"></i>Elimini unii filtri</li>
            </ul>
        </div>
        <div class="space-x-4">
            <a href="{% url 'marketplace:listings' %}" 
               class="inline-block bg-primary text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-list mr-2"></i>Vezi toate anunțurile
            </a>
            <a href="{% url 'marketplace:add_listing' %}" 
               class="inline-block bg-accent text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                <i class="fas fa-plus mr-2"></i>Adaugă anunț
            </a>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- No Search Query -->
    <div class="bg-white rounded-lg shadow-md p-12 text-center">
        <i class="fas fa-search text-6xl text-gray-300 mb-6"></i>
        <h3 class="text-2xl font-semibold text-gray-600 mb-4">Caută în mii de anunțuri</h3>
        <p class="text-gray-500 mb-8 max-w-md mx-auto">
            Completează formularul de mai sus pentru a găsi exact ce cauți
        </p>
        
        <!-- Popular Searches -->
        <div class="max-w-2xl mx-auto">
            <h4 class="text-lg font-semibold text-gray-700 mb-4">Căutări populare:</h4>
            <div class="flex flex-wrap justify-center gap-2">
                <a href="?q=iphone" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full hover:bg-gray-200 transition-colors">iPhone</a>
                <a href="?q=apartament" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full hover:bg-gray-200 transition-colors">Apartament</a>
                <a href="?q=masina" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full hover:bg-gray-200 transition-colors">Mașină</a>
                <a href="?q=laptop" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full hover:bg-gray-200 transition-colors">Laptop</a>
                <a href="?q=bicicleta" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full hover:bg-gray-200 transition-colors">Bicicletă</a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Location services for search
    const useCurrentLocationBtn = document.getElementById('use-current-location');
    const locationInput = document.getElementById('location');
    const enableDistanceCheckbox = document.getElementById('enable-distance');
    const distanceSelect = document.querySelector('select[name="distance"]');
    
    // Get current location for search
    useCurrentLocationBtn.addEventListener('click', function() {
        if ('geolocation' in navigator) {
            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    
                    // Add hidden fields for coordinates
                    let latInput = document.querySelector('input[name="user_lat"]');
                    let lngInput = document.querySelector('input[name="user_lng"]');
                    
                    if (!latInput) {
                        latInput = document.createElement('input');
                        latInput.type = 'hidden';
                        latInput.name = 'user_lat';
                        locationInput.parentNode.appendChild(latInput);
                    }
                    if (!lngInput) {
                        lngInput = document.createElement('input');
                        lngInput.type = 'hidden';
                        lngInput.name = 'user_lng';
                        locationInput.parentNode.appendChild(lngInput);
                    }
                    
                    latInput.value = lat;
                    lngInput.value = lng;
                    
                    // Reverse geocode to get city name
                    fetch(`/api/location/reverse-geocode/?lat=${lat}&lng=${lng}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                locationInput.value = data.city || data.display_name || 'Locația curentă';
                                // Enable distance search automatically
                                enableDistanceCheckbox.checked = true;
                                distanceSelect.disabled = false;
                            }
                            useCurrentLocationBtn.innerHTML = '<i class="fas fa-crosshairs"></i>';
                        })
                        .catch(error => {
                            console.error('Reverse geocoding error:', error);
                            locationInput.value = 'Locația curentă';
                            enableDistanceCheckbox.checked = true;
                            distanceSelect.disabled = false;
                            useCurrentLocationBtn.innerHTML = '<i class="fas fa-crosshairs"></i>';
                        });
                },
                function(error) {
                    console.error('Geolocation error:', error);
                    alert('Nu s-a putut accesa locația curentă. Te rog să introduci manual orașul.');
                    useCurrentLocationBtn.innerHTML = '<i class="fas fa-crosshairs"></i>';
                }
            );
        } else {
            alert('Browserul nu suportă geolocația. Te rog să introduci manual orașul.');
        }
    });
    
    // Enable/disable distance filter
    enableDistanceCheckbox.addEventListener('change', function() {
        distanceSelect.disabled = !this.checked;
        if (!this.checked) {
            // Remove coordinate inputs when disabled
            const latInput = document.querySelector('input[name="user_lat"]');
            const lngInput = document.querySelector('input[name="user_lng"]');
            if (latInput) latInput.remove();
            if (lngInput) lngInput.remove();
        }
    });
    
    // Dynamic subcategory loading for category filter
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            const categoryId = this.value;
            // You can add subcategory filtering here if needed
        });
    }
    
    // Auto-suggest for location input
    let locationTimeout;
    locationInput.addEventListener('input', function() {
        clearTimeout(locationTimeout);
        const query = this.value.trim();
        
        if (query.length > 2) {
            locationTimeout = setTimeout(() => {
                searchLocations(query);
            }, 500);
        }
    });
    
    function searchLocations(query) {
        fetch(`/api/location/search/?q=${encodeURIComponent(query)}&limit=5`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.results.length > 0) {
                    showLocationSuggestions(data.results);
                }
            })
            .catch(error => {
                console.error('Location search error:', error);
            });
    }
    
    function showLocationSuggestions(results) {
        // Remove existing suggestions
        const existing = document.getElementById('location-suggestions');
        if (existing) existing.remove();
        
        // Create suggestions dropdown
        const suggestions = document.createElement('div');
        suggestions.id = 'location-suggestions';
        suggestions.className = 'absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg mt-1';
        
        results.forEach(result => {
            const item = document.createElement('div');
            item.className = 'px-4 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0';
            item.innerHTML = `
                <div class="font-medium">${result.display_name}</div>
                <div class="text-sm text-gray-500">${result.city || ''} ${result.county || ''}</div>
            `;
            item.addEventListener('click', () => {
                locationInput.value = result.display_name;
                suggestions.remove();
                
                // Add coordinates for distance search
                let latInput = document.querySelector('input[name="user_lat"]');
                let lngInput = document.querySelector('input[name="user_lng"]');
                
                if (!latInput) {
                    latInput = document.createElement('input');
                    latInput.type = 'hidden';
                    latInput.name = 'user_lat';
                    locationInput.parentNode.appendChild(latInput);
                }
                if (!lngInput) {
                    lngInput = document.createElement('input');
                    lngInput.type = 'hidden';
                    lngInput.name = 'user_lng';
                    locationInput.parentNode.appendChild(lngInput);
                }
                
                latInput.value = result.latitude;
                lngInput.value = result.longitude;
                
                // Enable distance search
                enableDistanceCheckbox.checked = true;
                distanceSelect.disabled = false;
            });
            suggestions.appendChild(item);
        });
        
        locationInput.parentNode.style.position = 'relative';
        locationInput.parentNode.appendChild(suggestions);
        
        // Remove suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!locationInput.contains(e.target) && !suggestions.contains(e.target)) {
                suggestions.remove();
            }
        }, { once: true });
    }
});
</script>
{% endblock %}
{% endblock %}
