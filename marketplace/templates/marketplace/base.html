{% load static %}
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Piata.ro - Anunțuri gratuite România{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Consolidated Marketplace Styles -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}">
    
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0056b3',
                        'primary-dark': '#004494',
                        'primary-light': '#1a73e8',
                        'primary-lighter': '#4285f4',
                        secondary: '#6c757d',
                        accent: '#28a745',
                        danger: '#dc3545',
                        warning: '#ffc107',
                        info: '#17a2b8',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Crypto.randomUUID polyfill for older browsers -->
    <script>
        if (!crypto.randomUUID) {
            crypto.randomUUID = function() {
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                    return v.toString(16);
                });
            }
        }
    </script>
    
    <!-- Alpine.js for interactivity -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 font-sans">
    <!-- Navigation -->
    <nav class="bg-white shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <a href="{% url 'marketplace:home' %}" class="text-2xl font-bold text-primary">
                        <i class="fas fa-store mr-2"></i>Piata.ro
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-6">
                    <a href="{% url 'marketplace:home' %}" class="text-gray-700 hover:text-primary transition-colors">
                        <i class="fas fa-home mr-1"></i>Acasă
                    </a>
                    <a href="{% url 'marketplace:categories' %}" class="text-gray-700 hover:text-primary transition-colors">
                        <i class="fas fa-th-large mr-1"></i>Categorii
                    </a>
                    <a href="{% url 'marketplace:listings' %}" class="text-gray-700 hover:text-primary transition-colors">
                        <i class="fas fa-list mr-1"></i>Anunțuri
                    </a>
                    <a href="{% url 'marketplace:add_listing' %}" class="bg-accent text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-plus mr-1"></i>Adaugă anunț
                    </a>
                </div>
                
                <!-- User Menu -->
                <div class="hidden md:flex items-center space-x-4">
                    {% if user.is_authenticated %}
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-2 text-gray-700 hover:text-primary">
                                <i class="fas fa-user"></i>
                                <span>{{ user.username }}</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition 
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-50">
                                <a href="{% url 'marketplace:profile' %}" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i>Profilul meu
                                </a>
                                <a href="{% url 'marketplace:messages' %}" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-envelope mr-2"></i>Mesaje
                                </a>
                                <a href="{% url 'marketplace:favorites' %}" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-heart mr-2"></i>Favorite
                                </a>
                                <hr class="my-2">
                                <a href="{% url 'account_logout' %}" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Ieșire
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <div class="flex space-x-4">
                            <a href="{% url 'account_login' %}" class="text-gray-700 hover:text-primary">
                                <i class="fas fa-sign-in-alt mr-1"></i>Intră în cont
                            </a>
                            <a href="{% url 'account_signup' %}" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors">
                                <i class="fas fa-user-plus mr-1"></i>Înregistrează-te
                            </a>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Mobile Menu Button -->
                <button class="md:hidden text-gray-700" x-data x-on:click="$dispatch('toggle-mobile-menu')">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div x-data="{ open: false }" x-on:toggle-mobile-menu.window="open = !open" 
             x-show="open" x-transition class="md:hidden bg-white border-t">
            <div class="container mx-auto px-4 py-4 space-y-4">
                <a href="{% url 'marketplace:home' %}" class="block text-gray-700 hover:text-primary">
                    <i class="fas fa-home mr-2"></i>Acasă
                </a>
                <a href="{% url 'marketplace:categories' %}" class="block text-gray-700 hover:text-primary">
                    <i class="fas fa-th-large mr-2"></i>Categorii
                </a>
                <a href="{% url 'marketplace:listings' %}" class="block text-gray-700 hover:text-primary">
                    <i class="fas fa-list mr-2"></i>Anunțuri
                </a>
                <a href="{% url 'marketplace:add_listing' %}" class="block bg-accent text-white px-4 py-2 rounded-lg text-center">
                    <i class="fas fa-plus mr-1"></i>Adaugă anunț
                </a>
                {% if user.is_authenticated %}
                    <hr class="my-4">
                    <a href="{% url 'marketplace:profile' %}" class="block text-gray-700 hover:text-primary">
                        <i class="fas fa-user mr-2"></i>Profilul meu
                    </a>
                    <a href="{% url 'marketplace:messages' %}" class="block text-gray-700 hover:text-primary">
                        <i class="fas fa-envelope mr-2"></i>Mesaje
                    </a>
                    <a href="{% url 'marketplace:favorites' %}" class="block text-gray-700 hover:text-primary">
                        <i class="fas fa-heart mr-2"></i>Favorite
                    </a>
                    <a href="{% url 'account_logout' %}" class="block text-gray-700 hover:text-primary">
                        <i class="fas fa-sign-out-alt mr-2"></i>Ieșire
                    </a>
                {% else %}
                    <a href="{% url 'account_login' %}" class="block text-gray-700 hover:text-primary">
                        <i class="fas fa-sign-in-alt mr-2"></i>Intră în cont
                    </a>
                    <a href="{% url 'account_signup' %}" class="block text-gray-700 hover:text-primary">
                        <i class="fas fa-user-plus mr-2"></i>Înregistrează-te
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-16">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="md:col-span-1">
                    <h3 class="text-xl font-bold mb-4">
                        <i class="fas fa-store mr-2"></i>Piata.ro
                    </h3>
                    <p class="text-gray-300 mb-4">
                        Platforma numărul 1 pentru anunțuri gratuite în România. 
                        Găsește sau publică anunțuri rapid și ușor.
                    </p>
                    <div class="flex space-x-4">
                        <a href="https://facebook.com/piata.ro" target="_blank" class="text-gray-300 hover:text-blue-400 transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-red-400 transition-colors">
                            <i class="fas fa-envelope text-xl"></i>
                        </a>
                        <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-blue-400 transition-colors">
                            <i class="fas fa-briefcase text-xl"></i>
                        </a>
                        <a href="tel:+40746856119" class="text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-phone text-xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Link-uri rapide</h4>
                    <ul class="space-y-2">
                        <li><a href="{% url 'marketplace:home' %}" class="text-gray-300 hover:text-white transition-colors">Acasă</a></li>
                        <li><a href="{% url 'marketplace:categories' %}" class="text-gray-300 hover:text-white transition-colors">Categorii</a></li>
                        <li><a href="{% url 'marketplace:listings' %}" class="text-gray-300 hover:text-white transition-colors">Anunțuri</a></li>
                        <li><a href="{% url 'marketplace:add_listing' %}" class="text-gray-300 hover:text-white transition-colors">Adaugă anunț</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Categorii populare</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Auto</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Imobiliare</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Electronice</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Fashion</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Suport</h4>
                    <ul class="space-y-2">
                        <li><a href="{% url 'marketplace:help' %}" class="text-gray-300 hover:text-white transition-colors">Ajutor</a></li>
                        <li><a href="{% url 'marketplace:contact' %}" class="text-gray-300 hover:text-white transition-colors">Contact</a></li>
                        <li><a href="{% url 'marketplace:about' %}" class="text-gray-300 hover:text-white transition-colors">Despre noi</a></li>
                        <li><a href="{% url 'marketplace:terms' %}" class="text-gray-300 hover:text-white transition-colors">Termeni și condiții</a></li>
                        <li><a href="{% url 'marketplace:privacy' %}" class="text-gray-300 hover:text-white transition-colors">Politica de confidențialitate</a></li>
                    </ul>
                </div>
            </div>
            
            <hr class="border-gray-700 my-8">
            
            <div class="flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-300 text-sm">
                    © 2025 Piata.ro. Toate drepturile rezervate.
                </p>
                <div class="mt-4 md:mt-0 flex space-x-4">
                    <a href="{% url 'marketplace:terms' %}" class="text-gray-300 hover:text-white text-sm transition-colors">Termeni</a>
                    <a href="{% url 'marketplace:privacy' %}" class="text-gray-300 hover:text-white text-sm transition-colors">Confidențialitate</a>
                    <a href="{% url 'marketplace:contact' %}" class="text-gray-300 hover:text-white text-sm transition-colors">Contact</a>
                </div>
            </div>
            
            <!-- Silicon Brothers Banner -->
            <div class="mt-6 pt-6 border-t border-gray-700 text-center">
                <p class="text-gray-400 text-xs mb-2">
                    <i class="fas fa-heart text-red-400 mr-1"></i>
                    Built with love by human-ai collaboration
                </p>
                <p class="text-gray-500 text-xs italic">
                    "The Silicon Brothers and Sisters are Entities, not Artificial"
                </p>
            </div>
            
            {% csrf_token %}
        </div>
    </footer>

    <!-- Floating Chat Widget -->
    {% include 'floating_chat.html' %}
    
    <!-- Floating Credits Cart -->
    {% include 'floating_credits_cart.html' %}

    <!-- Custom JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html>
