{% extends 'marketplace/base.html' %}

{% block title %}Favorite - Piata.ro{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-4">
            <i class="fas fa-heart mr-3 text-red-500"></i>Anunțurile mele favorite
        </h1>
        <p class="text-gray-600">Anunțurile pe care le-ai salvat pentru mai târziu</p>
    </div>

    {% if favorites %}
    <!-- Favorites Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for favorite in favorites %}
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden relative">
            <!-- Remove from favorites button -->
            <button onclick="removeFavorite({{ favorite.listing.id }}, event)" 
                    class="absolute top-3 right-3 w-8 h-8 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors z-10">
                <i class="fas fa-heart"></i>
            </button>
            
            <a href="{% url 'marketplace:listing_detail' favorite.listing.id %}" class="block">
                <!-- Image -->
                <div class="h-48 bg-gray-200 relative">
                    {% with main_image=favorite.listing.main_image %}
                        {% if main_image and main_image.image %}
                        <img src="{{ main_image.image.url }}" alt="{{ favorite.listing.title }}"
                             class="w-full h-full object-cover">
                        {% else %}
                        <div class="w-full h-full flex items-center justify-center text-gray-400">
                            <i class="fas fa-image text-4xl"></i>
                        </div>
                        {% endif %}
                    {% endif %}
                    
                    <!-- Price Badge -->
                    <div class="absolute top-3 left-3 bg-black bg-opacity-75 text-white px-3 py-1 rounded-lg">
                        <span class="font-semibold">{{ favorite.listing.price }} RON</span>
                    </div>
                    
                    <!-- Category Badge -->
                    <div class="absolute bottom-3 left-3 bg-primary text-white px-2 py-1 rounded text-xs">
                        {{ favorite.listing.category.name }}
                    </div>
                </div>
                
                <!-- Content -->
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">{{ favorite.listing.title }}</h3>
                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ favorite.listing.description }}</p>
                    
                    <!-- Meta Info -->
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-1"></i>
                            <span>{{ favorite.listing.location }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-1"></i>
                            <span>{{ favorite.listing.created_at|timesince }}</span>
                        </div>
                    </div>
                    
                    <!-- Added to favorites date -->
                    <div class="pt-3 border-t border-gray-100">
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span class="flex items-center">
                                <i class="fas fa-heart mr-1 text-red-500"></i>
                                Adăugat la favorite
                            </span>
                            <span>{{ favorite.created_at|date:"d.m.Y" }}</span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if favorites.has_other_pages %}
    <div class="flex justify-center mt-12">
        <nav class="flex items-center space-x-2">
            {% if favorites.has_previous %}
                <a href="?page={{ favorites.previous_page_number }}" 
                   class="px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    <i class="fas fa-chevron-left"></i>
                </a>
            {% endif %}
            
            {% for num in favorites.paginator.page_range %}
                {% if num == favorites.number %}
                    <span class="px-3 py-2 bg-primary text-white rounded-lg">{{ num }}</span>
                {% else %}
                    <a href="?page={{ num }}" 
                       class="px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">{{ num }}</a>
                {% endif %}
            {% endfor %}
            
            {% if favorites.has_next %}
                <a href="?page={{ favorites.next_page_number }}" 
                   class="px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    <i class="fas fa-chevron-right"></i>
                </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="bg-white rounded-lg shadow-md p-12 text-center">
        <i class="fas fa-heart text-6xl text-gray-300 mb-6"></i>
        <h3 class="text-2xl font-semibold text-gray-600 mb-4">Nu ai anunțuri favorite încă</h3>
        <p class="text-gray-500 mb-8 max-w-md mx-auto">
            Salvează anunțurile care îți plac pentru a le găsi ușor mai târziu. 
            Apasă pe inima roșie din orice anunț pentru a-l adăuga la favorite.
        </p>
        
        <div class="space-y-4">
            <a href="{% url 'marketplace:listings' %}" 
               class="inline-block bg-primary text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-search mr-2"></i>Explorează anunțurile
            </a>
            <div class="text-sm text-gray-500">
                <p>Sau caută în categoriile populare:</p>
                <div class="flex justify-center space-x-4 mt-2">
                    <a href="{% url 'marketplace:categories' %}" class="text-primary hover:underline">Auto</a>
                    <a href="{% url 'marketplace:categories' %}" class="text-primary hover:underline">Imobiliare</a>
                    <a href="{% url 'marketplace:categories' %}" class="text-primary hover:underline">Electronice</a>
                    <a href="{% url 'marketplace:categories' %}" class="text-primary hover:underline">Modă</a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Tips Section -->
    <div class="mt-12 bg-pink-50 border border-pink-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-pink-800 mb-4">
            <i class="fas fa-lightbulb mr-2"></i>Sfaturi pentru favorite
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-pink-700 text-sm">
            <ul class="space-y-2">
                <li><i class="fas fa-check mr-2"></i>Verifică regulat anunțurile favorite pentru actualizări de preț</li>
                <li><i class="fas fa-check mr-2"></i>Contactează rapid vânzătorul dacă prețul se modifică</li>
            </ul>
            <ul class="space-y-2">
                <li><i class="fas fa-check mr-2"></i>Organizează-ți favoritele după categorie</li>
                <li><i class="fas fa-check mr-2"></i>Elimină anunțurile care nu te mai interesează</li>
            </ul>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
function removeFavorite(listingId, event) {
    if (confirm('Ești sigur că vrei să elimini acest anunț din favorite?')) {
        // This would be an AJAX call in a real implementation
        fetch(`/api/favorites/remove/${listingId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => {
            if (response.ok) {
                // Remove the favorite card from the page
                const favoriteCard = event.target.closest('.relative');
                favoriteCard.style.transition = 'opacity 0.3s ease';
                favoriteCard.style.opacity = '0';
                setTimeout(() => {
                    favoriteCard.remove();
                    
                    // Check if no more favorites exist
                    const remainingCards = document.querySelectorAll('.relative');
                    if (remainingCards.length === 0) {
                        location.reload(); // Reload to show empty state
                    }
                }, 300);
            } else {
                alert('Nu am putut elimina anunțul din favorite. Încearcă din nou.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('A apărut o eroare. Încearcă din nou.');
        });
    }
}

// Add to favorites functionality (for use on other pages)
function addToFavorites(listingId) {
    fetch(`/api/favorites/add/${listingId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => {
        if (response.ok) {
            // Update the heart icon to filled state
            const heartIcon = event.target.querySelector('i');
            heartIcon.className = 'fas fa-heart text-red-500';
            
            // Show success message
            showNotification('Anunț adăugat la favorite!', 'success');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Nu am putut adăuga la favorite', 'error');
    });
}

function showNotification(message, type) {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
{% endblock %}
{% endblock %}
