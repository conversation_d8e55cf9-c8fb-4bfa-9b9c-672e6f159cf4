# Gemini CLI Setup Guide for Piața.ro

This guide provides instructions for installing and configuring the Google Gemini CLI to interact with Gemini models for development and testing purposes within the Piața.ro project.

## Step 1: Install the Gemini CLI

The official installation script handles the download and setup. Run the following command in your terminal:

```bash
curl -o google-ai-cli.sh https://storage.googleapis.com/cloud-sdk-repo/downloads/google-ai-cli/install.sh
bash google-ai-cli.sh
```

The script will install the `gemini` executable to `$HOME/.local/bin`.

## Step 2: Update Your Shell Environment

For the `gemini` command to be immediately available, you need to reload your shell's configuration file or open a new terminal session.

```bash
# For bash users
source ~/.bashrc

# For zsh users
source ~/.zshrc
```

## Step 3: Authenticate with Your API Key

1.  **Get an API Key**: Go to Google AI Studio and create a new API key.
2.  **Run the Auth Command**: Execute the following command and paste your API key when prompted.

    ```bash
    gemini auth
    ```

## Step 4: Verify and Test

You can verify the installation by checking the version and running a simple prompt.

```bash
# Check the version
gemini --version

# Send a test prompt
gemini prompt "What are the most popular e-commerce platforms in Romania?"
```