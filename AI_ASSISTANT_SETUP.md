# Piața.ro AI Assistant - Complete Setup Guide

## Overview
This AI assistant provides comprehensive marketplace management with MCP (Model Context Protocol) integration, supporting code refactoring, marketing automation, and VSCode integration.

## Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    AI Assistant System                       │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Refact MCP    │  │ Marketing MCP   │  │ Database MCP│ │
│  │   Server        │  │   Server        │  │   Server    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   VSCode        │  │   n8n           │  │  Flowise    │ │
│  │   Extension     │  │   Integration   │  │  Agents     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Components

### 1. Core AI Assistant
- **Location**: `ai_assistant/views.py`
- **Admin Interface**: Django admin at `/admin/ai-assistant/`
- **Features**: Conversation management, MCP integration, real-time status

### 2. Refact MCP Server
- **Location**: `ai_assistant/mcp_refact_server.py`
- **Purpose**: Code analysis and refactoring
- **Tools**:
  - `analyze_code`: Analyze code for issues
  - `get_refactoring_suggestions`: Get specific suggestions
  - `apply_refactoring`: Apply changes automatically
  - `detect_code_smells`: Find anti-patterns
  - `optimize_imports`: Clean up imports

### 3. Marketing Automation Agent
- **Location**: `agents/marketing_automation_agent.yaml`
- **Platforms**: TikTok, Instagram, Facebook, Google Ads
- **Integrations**: n8n, Flowise, Azure MCP

### 4. VSCode Integration
- **Location**: `ai_assistant/vscode_integration.py`
- **Features**: Direct file analysis, refactoring, optimization
- **Usage**: CLI commands and VSCode extension

## Quick Start

### 1. Start the AI Assistant
```bash
# Start Django server
python manage.py runserver

# Start Refact MCP Server (in new terminal)
./start_refact_agent.sh
```

### 2. Access Admin Interface
- Navigate to: `http://localhost:8000/admin/ai-assistant/`
- Login with admin credentials
- Start conversations with the AI assistant

### 3. VSCode Integration
```bash
# Analyze current file
python ai_assistant/vscode_integration.py analyze path/to/file.py

# Get refactoring suggestions
python ai_assistant/vscode_integration.py suggest path/to/file.py --type performance

# Detect code smells
python ai_assistant/vscode_integration.py smells path/to/file.py

# Optimize imports
python ai_assistant/vscode_integration.py optimize path/to/file.py
```

## MCP Server Usage

### Direct API Calls
```python
import requests

# Analyze code
response = requests.post('http://localhost:8000/ai-assistant/mcp/analyze', json={
    "code": "your_code_here",
    "language": "python",
    "focus_areas": ["performance", "readability"]
})

# Get suggestions
response = requests.post('http://localhost:8000/ai-assistant/mcp/suggestions', json={
    "code": "your_code_here",
    "language": "python",
    "suggestion_type": "performance"
})
```

## Marketing Automation Setup

### 1. n8n Integration
```yaml
# Configure n8n webhook
n8n_webhook: "https://your-n8n-instance.com/webhook/piata-ai"
```

### 2. Flowise Integration
```yaml
# Configure Flowise agents
flowise_api: "https://your-flowise-instance.com/api/v1"
```

### 3. Azure MCP
```yaml
# Azure configuration
azure_mcp:
  endpoint: "https://your-azure-mcp.cognitiveservices.azure.com"
  key: "your-azure-key"
```

## Environment Variables
Create `.env` file:
```bash
# AI Assistant
AI_ASSISTANT_BASE_URL=http://localhost:8000
AI_ASSISTANT_API_KEY=your-secret-key

# MCP Servers
REFACT_MCP_PORT=8004
MARKETING_MCP_PORT=8005

# External Integrations
N8N_WEBHOOK_URL=https://your-n8n.com/webhook
FLOWISE_API_URL=https://your-flowise.com/api
AZURE_MCP_ENDPOINT=https://your-azure.cognitiveservices.azure.com
```

## VSCode Extension Commands
When the extension is installed, use these commands:
- `Ctrl+Shift+P` → "Refact: Analyze Code"
- `Ctrl+Shift+P` → "Refact: Get Suggestions"
- `Ctrl+Shift+P` → "Refact: Apply Changes"
- `Ctrl+Shift+P` → "Refact: Optimize Imports"

## Future-Ready Features

### 1. Marketing Automation
- **TikTok Ads**: Auto-generate video scripts, hashtags, posting schedules
- **Instagram**: Story templates, feed posts, engagement monitoring
- **Facebook**: Campaign optimization, audience targeting, A/B testing
- **Google Ads**: Keyword expansion, bid optimization, ad copy refresh

### 2. Scaling Architecture
- **Auto-scaling**: Handles traffic spikes automatically
- **Load balancing**: Distributes load across multiple instances
- **Caching**: Redis-based caching for performance
- **CDN**: Global content delivery

### 3. Monitoring & Analytics
- **Real-time metrics**: Engagement, conversion, ROAS
- **Alert system**: Performance drops, budget alerts
- **Daily reports**: Email, Slack, dashboard notifications

## Development Workflow

### 1. Local Development
```bash
# Start development server
python manage.py runserver

# Start MCP servers
./start_refact_agent.sh

# Test VSCode integration
python ai_assistant/vscode_integration.py analyze test.py
```

### 2. Testing
```bash
# Run tests
python manage.py test ai_assistant

# Test MCP endpoints
curl -X POST http://localhost:8004/analyze -d '{"code": "print(\"hello\")", "language": "python"}'
```

### 3. Production Deployment
```bash
# Deploy to Azure
az webapp up --name piata-ai-assistant

# Configure production settings
export DJANGO_SETTINGS_MODULE=piata_ro.settings_prod
```

## Troubleshooting

### Common Issues

1. **MCP Server Not Starting**
   ```bash
   # Check Python path
   export PYTHONPATH="${PYTHONPATH}:$(pwd)"
   
   # Check dependencies
   pip install mcp
   ```

2. **VSCode Integration Not Working**
   ```bash
   # Check server is running
   curl http://localhost:8000/ai-assistant/status
   
   # Check file permissions
   chmod +x ai_assistant/vscode_integration.py
   ```

3. **Marketing Automation Issues**
   ```bash
   # Check webhook URLs
   curl -I $N8N_WEBHOOK_URL
   
   # Check API keys
   echo $AZURE_MCP_KEY | wc -c
   ```

## Support
- **Documentation**: Check individual component READMEs
- **Issues**: Create GitHub issues for bugs
- **Discord**: Join our development community
- **Email**: <EMAIL>

## Next Steps
1. Set up your environment variables
2. Start the Django server
3. Configure your MCP servers
4. Install VSCode extension
5. Set up n8n and Flowise integrations
6. Deploy to Azure for production use
