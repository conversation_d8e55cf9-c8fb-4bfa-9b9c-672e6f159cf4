name: 🤖 AI Assistant Request
description: Request help from sourcey.ai or OpenHands for development tasks
title: "[AI] "
labels: ["ai-assistance", "help-wanted"]
body:
  - type: markdown
    attributes:
      value: |
        ## 🤖 AI Assistant Request
        Use this template to request help from AI assistants like sourcey.ai or OpenHands for development tasks.

  - type: dropdown
    id: ai-assistant
    attributes:
      label: 🤖 Preferred AI Assistant
      description: Which AI assistant would be best for this task?
      options:
        - 🧠 Sourcey.ai - Code generation and analysis
        - 🔧 OpenHands - Development automation
        - 🤝 Either - No preference
        - 👥 Both - Collaborative approach needed
    validations:
      required: true

  - type: dropdown
    id: task-type
    attributes:
      label: 📋 Task Type
      description: What type of development task do you need help with?
      options:
        - 🔧 Code Generation - Create new functionality
        - 🐛 Bug Fixing - Debug and fix issues
        - 🔄 Refactoring - Improve existing code
        - 📊 Code Analysis - Review and optimize
        - 🧪 Testing - Create or improve tests
        - 📚 Documentation - Generate or update docs
        - 🎨 UI/UX - Frontend improvements
        - 🔍 Code Review - Analyze pull requests
        - 🚀 Performance - Optimize performance
        - 🔐 Security - Security analysis/improvements
        - 🌐 Integration - API/service integration
        - 📱 Mobile - Responsive/mobile improvements
    validations:
      required: true

  - type: textarea
    id: task-description
    attributes:
      label: 📝 Task Description
      description: Clearly describe what you need help with
      placeholder: |
        Provide a detailed description of the task:
        - What needs to be done?
        - What is the current state?
        - What should the end result be?
    validations:
      required: true

  - type: dropdown
    id: marketplace-area
    attributes:
      label: 🏪 Marketplace Component
      description: Which part of the Romanian marketplace is this related to?
      options:
        - 🏠 Homepage/Landing page
        - 🔍 Search functionality
        - 📋 Listing creation/management
        - 🏷️ Category management
        - 👤 User authentication/profiles
        - 💬 Messaging system
        - 💰 Payment/Credit system
        - 📱 Mobile experience
        - 🔧 Admin panel
        - 📊 Analytics/reporting
        - 🌐 API endpoints
        - 🗄️ Database operations
        - 🔐 Security features
        - 🌍 Localization (Romanian)
    validations:
      required: true

  - type: textarea
    id: context-files
    attributes:
      label: 📁 Relevant Files
      description: List the files that are relevant to this task
      placeholder: |
        - marketplace/templates/marketplace/index.html
        - api/models.py
        - marketplace/views.py
        - etc.
    validations:
      required: true

  - type: textarea
    id: current-code
    attributes:
      label: 💻 Current Code Context
      description: Provide any relevant code snippets or current implementation
      placeholder: |
        ```python
        # Current implementation
        def current_function():
            pass
        ```
      render: markdown

  - type: textarea
    id: requirements
    attributes:
      label: 📋 Specific Requirements
      description: What are the specific requirements or constraints?
      placeholder: |
        - Must work with Python 3.8+
        - Should follow existing code patterns
        - Needs to support Romanian language
        - Must be mobile-responsive
        - Should integrate with existing authentication
    validations:
      required: true

  - type: textarea
    id: expected-outcome
    attributes:
      label: 🎯 Expected Outcome
      description: What should the final result look like?
      placeholder: |
        Describe the expected outcome:
        - What functionality should be available?
        - How should it behave?
        - What should the user experience be?
    validations:
      required: true

  - type: dropdown
    id: complexity
    attributes:
      label: ⚡ Complexity Level
      description: How complex do you think this task is?
      options:
        - 🟢 Simple - Straightforward implementation
        - 🟡 Medium - Requires some analysis
        - 🔴 Complex - Needs deep understanding
        - 🟣 Very Complex - Multiple components involved
    validations:
      required: true

  - type: dropdown
    id: urgency
    attributes:
      label: ⏰ Urgency
      description: How urgent is this task?
      options:
        - 🔥 Critical - Blocking development
        - ⚠️ High - Needed soon
        - 🟡 Medium - Important but not urgent
        - 🟢 Low - Can wait
    validations:
      required: true

  - type: textarea
    id: constraints
    attributes:
      label: 🚫 Constraints & Limitations
      description: Any constraints or limitations to consider?
      placeholder: |
        - Cannot modify existing database schema
        - Must maintain backward compatibility
        - Should not break existing functionality
        - Performance requirements
        - Romanian market compliance

  - type: checkboxes
    id: romanian-context
    attributes:
      label: 🇷🇴 Romanian Market Context
      description: Romanian-specific considerations
      options:
        - label: Requires Romanian language support
        - label: Must comply with Romanian regulations
        - label: Should follow Romanian UX patterns
        - label: Needs RON currency formatting
        - label: Should integrate with Romanian services
        - label: Must consider Romanian user behavior

  - type: textarea
    id: success-criteria
    attributes:
      label: ✅ Success Criteria
      description: How do we know when this task is successfully completed?
      placeholder: |
        - [ ] Functionality works as expected
        - [ ] Code follows project standards
        - [ ] Tests are passing
        - [ ] Documentation is updated
        - [ ] Romanian localization is correct
    validations:
      required: true

  - type: textarea
    id: additional-context
    attributes:
      label: 📝 Additional Context
      description: Any other information that might help the AI assistant?
      placeholder: |
        - Related issues or PRs
        - Relevant documentation
        - Examples from other parts of the codebase
        - External references or inspiration

  - type: checkboxes
    id: ai-permissions
    attributes:
      label: 🔑 AI Assistant Permissions
      description: What is the AI assistant allowed to do?
      options:
        - label: Generate new code files
        - label: Modify existing files
        - label: Create database migrations
        - label: Update documentation
        - label: Suggest architectural changes
        - label: Create test files
        - label: Modify configuration files

  - type: textarea
    id: human-review
    attributes:
      label: 👥 Human Review Requirements
      description: What aspects require human review?
      placeholder: |
        - Business logic validation
        - Security review
        - Romanian localization accuracy
        - User experience validation
        - Performance testing

  - type: checkboxes
    id: checklist
    attributes:
      label: ✅ Checklist
      description: Please confirm you have done the following
      options:
        - label: I have provided clear task description
          required: true
        - label: I have listed relevant files and context
          required: true
        - label: I have specified success criteria
          required: true
        - label: I have considered Romanian market requirements
          required: true
