name: 🐛 Bug Report
description: Report a bug or issue with the Romanian marketplace
title: "[BUG] "
labels: ["bug", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        ## 🐛 Bug Report
        Thanks for taking the time to report a bug! Please fill out the information below to help us resolve the issue quickly.

  - type: textarea
    id: bug-description
    attributes:
      label: 🔍 Bug Description
      description: A clear and concise description of what the bug is
      placeholder: Describe what happened and what you expected to happen
    validations:
      required: true

  - type: dropdown
    id: bug-area
    attributes:
      label: 🏪 Marketplace Area
      description: Which part of the marketplace is affected?
      options:
        - Frontend - User Interface
        - Backend - API/Server
        - Database - Data Storage
        - Authentication - Login/Register
        - Search - Finding listings
        - Listings - Creating/Editing posts
        - Categories - Product organization
        - User Profiles - Account management
        - Payment/Credits - Transaction system
        - Mobile - Responsive design
        - Other - Please specify in description
    validations:
      required: true

  - type: textarea
    id: reproduction-steps
    attributes:
      label: 🔄 Steps to Reproduce
      description: How can we reproduce this bug?
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: ✅ Expected Behavior
      description: What should have happened?
      placeholder: Describe the expected behavior
    validations:
      required: true

  - type: textarea
    id: actual-behavior
    attributes:
      label: ❌ Actual Behavior
      description: What actually happened?
      placeholder: Describe what actually happened
    validations:
      required: true

  - type: dropdown
    id: severity
    attributes:
      label: 🚨 Severity Level
      description: How severe is this bug?
      options:
        - 🔥 Critical - Site is broken/unusable
        - ⚠️ High - Major feature not working
        - 🟡 Medium - Minor feature issue
        - 🟢 Low - Cosmetic/minor issue
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: 🌐 Environment
      description: Please provide your environment details
      placeholder: |
        - OS: [e.g. Windows 10, macOS 12, Ubuntu 20.04]
        - Browser: [e.g. Chrome 96, Firefox 95, Safari 15]
        - Device: [e.g. Desktop, Mobile, Tablet]
        - Screen size: [e.g. 1920x1080, Mobile portrait]
      render: markdown
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: 📸 Screenshots/Videos
      description: If applicable, add screenshots or videos to help explain the problem
      placeholder: Drag and drop images here or paste image URLs

  - type: textarea
    id: console-errors
    attributes:
      label: 🔧 Console Errors
      description: Any error messages in browser console or server logs?
      placeholder: Paste any relevant error messages here
      render: shell

  - type: textarea
    id: additional-context
    attributes:
      label: 📝 Additional Context
      description: Add any other context about the problem here
      placeholder: Any additional information that might help us understand the issue

  - type: checkboxes
    id: romanian-specific
    attributes:
      label: 🇷🇴 Romanian Market Specific
      description: Is this related to Romanian market features?
      options:
        - label: Romanian language/localization issue
        - label: Romanian address/location problem
        - label: RON currency formatting issue
        - label: Local business rules problem

  - type: checkboxes
    id: checklist
    attributes:
      label: ✅ Checklist
      description: Please confirm you have done the following
      options:
        - label: I have searched existing issues to avoid duplicates
          required: true
        - label: I have provided clear steps to reproduce
          required: true
        - label: I have included relevant screenshots/videos (if applicable)
          required: false
