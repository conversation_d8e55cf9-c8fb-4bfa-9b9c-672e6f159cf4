## 🔧 Pull Request Summary

**Brief Description:** 
<!-- Provide a clear, concise description of what this PR accomplishes -->

**Related Issue(s):** 
<!-- Link to related issues using #issue_number -->
- Closes #
- Relates to #

## 🎯 Type of Change

Please check the relevant option(s):

- [ ] 🐛 **Bug fix** (non-breaking change which fixes an issue)
- [ ] ✨ **New feature** (non-breaking change which adds functionality)
- [ ] 💥 **Breaking change** (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 **Documentation** (changes to documentation only)
- [ ] 🎨 **Style/UI** (changes to styling, UI components, or visual elements)
- [ ] 🔧 **Refactoring** (code changes that neither fix a bug nor add a feature)
- [ ] ⚡ **Performance** (changes that improve performance)
- [ ] 🧪 **Tests** (adding missing tests or correcting existing tests)

## 🏪 Marketplace Impact

<!-- Describe how this change affects the marketplace functionality -->

**Frontend Changes:**
- [ ] UI/UX improvements
- [ ] New page/component added
- [ ] Django template updates
- [ ] Responsive design adjustments

**Backend Changes:**
- [ ] API endpoints modified/added
- [ ] Database schema changes
- [ ] Authentication/authorization updates
- [ ] Business logic modifications

**Infrastructure:**
- [ ] Docker configuration changes
- [ ] Migration scripts added
- [ ] Environment configuration updates

## 🚀 AI Assistant Context

<!-- Information for sourcey.ai and OpenHands -->

**AI Assistant Used:** 
- [ ] 🤖 **Sourcey.ai** - Code generation and optimization
- [ ] 🔧 **OpenHands** - Development assistance and debugging
- [ ] 👨‍💻 **Manual development** - Human-written code
- [ ] 🤝 **Hybrid** - AI-assisted with human review

**AI Prompts/Context:**
<!-- If AI was used, provide context about the prompts or requests made -->
```
Prompt: [Describe the AI prompt or request that led to these changes]
Context: [Any specific context or requirements provided to the AI]
```

## 📋 Changes Made

### Files Modified:
<!-- List the main files that were changed -->
- `filename.py` - Description of changes
- `component.py` - Description of changes
- `README.md` - Documentation updates

### Key Features Added:
<!-- Detailed list of new functionality -->
1. **Feature 1:** Description
2. **Feature 2:** Description
3. **Feature 3:** Description

### Bug Fixes:
<!-- List of bugs fixed -->
1. **Issue:** Description of the problem
   - **Solution:** How it was resolved
   - **Impact:** Who/what this affects

## 🧪 Testing

**Testing Performed:**
- [ ] Unit tests added/updated
- [ ] Integration tests passed
- [ ] Manual testing completed
- [ ] Cross-browser testing (Chrome, Firefox, Safari)
- [ ] Mobile responsiveness verified
- [ ] API endpoints tested

**Test Cases:**
<!-- Describe specific test scenarios -->
1. **Scenario 1:** Expected behavior verified
2. **Scenario 2:** Edge case handled
3. **Scenario 3:** Error handling tested

## 📱 Screenshots/Demo

<!-- Add screenshots or GIFs demonstrating the changes -->

**Before:**
<!-- Screenshot of before state -->

**After:**
<!-- Screenshot of after state -->

**Mobile View:**
<!-- Mobile screenshots if applicable -->

## 🔍 Code Quality Checklist

- [ ] Code follows project coding standards
- [ ] Functions are properly documented
- [ ] No console.log or debug statements left in code
- [ ] Error handling implemented where needed
- [ ] Code is readable and well-commented
- [ ] No hardcoded values (use environment variables)
- [ ] Performance implications considered
- [ ] Security implications reviewed

## 🌐 Romanian Marketplace Specific

**Localization:**
- [ ] Romanian language support maintained
- [ ] Currency formatting (RON) handled correctly
- [ ] Romanian address/location formats supported
- [ ] Local business rules implemented

**Marketplace Features:**
- [ ] Search functionality works correctly
- [ ] Category filtering operational
- [ ] User authentication flows tested
- [ ] Listing creation/editing verified
- [ ] Credit system functionality maintained

## 🚀 Deployment Checklist

- [ ] Environment variables documented
- [ ] Database migrations included (if needed)
- [ ] Docker configuration updated (if needed)
- [ ] Dependencies updated in requirements.txt/package.json
- [ ] No breaking changes to existing API
- [ ] Backward compatibility maintained

## 📝 Additional Notes

<!-- Any additional information, concerns, or context -->

## 🤝 Reviewer Guidelines

**For Human Reviewers:**
- Focus on business logic and marketplace functionality
- Verify Romanian localization accuracy
- Check for security vulnerabilities
- Ensure code maintainability

**For AI Reviewers (sourcey.ai/OpenHands):**
- Analyze code quality and best practices
- Check for potential bugs or edge cases
- Suggest performance optimizations
- Verify coding standards compliance

---

**By submitting this PR, I confirm that:**
- [ ] I have tested my changes thoroughly
- [ ] My code follows the project's coding standards
- [ ] I have documented any new functionality
- [ ] This PR is ready for review and deployment
