/* Modern Authentication Pages Styling */

:root {
  --primary: #0056b3;
  --primary-dark: #004494;
  --primary-light: #1a73e8;
  --accent: #28a745;
  --danger: #dc3545;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
}

/* Auth Container */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
}

/* Auth Card */
.auth-card {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

/* <PERSON>er */
.auth-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  padding: 2.5rem 2rem;
  text-align: center;
  position: relative;
}

.auth-header::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 0;
  right: 0;
  height: 40px;
  background: white;
  border-radius: 50% 50% 0 0;
}

.auth-header .icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  width: 80px;
  height: 80px;
  line-height: 80px;
  border-radius: 50%;
}

.auth-header h2 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0.5rem 0;
}

.auth-header p {
  font-size: 1rem;
  opacity: 0.9;
}

/* Form Styling */
.auth-form {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border: 2px solid var(--gray-300);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.15);
  outline: none;
}

.form-check {
  display: flex;
  align-items: center;
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin-right: 0.5rem;
  accent-color: var(--primary);
}

.form-check-label {
  font-size: 0.9rem;
  color: var(--gray-700);
}

/* Submit Button */
.submit-btn {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.submit-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Social Login */
.social-login {
  margin-top: 2rem;
  text-align: center;
}

.social-login p {
  font-size: 0.9rem;
  color: var(--gray-600);
  margin-bottom: 1rem;
  position: relative;
}

.social-login p::before,
.social-login p::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 30%;
  height: 1px;
  background: var(--gray-300);
}

.social-login p::before {
  left: 0;
}

.social-login p::after {
  right: 0;
}

.social-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.socialaccount_provider {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--gray-100);
  color: var(--gray-800);
  font-size: 1.5rem;
  transition: all 0.2s ease;
  text-decoration: none;
}

.socialaccount_provider:hover {
  background: var(--gray-200);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.socialaccount_provider.google {
  color: #DB4437;
}

.socialaccount_provider.facebook {
  color: #4267B2;
}

/* Alert Styling */
.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  color: var(--danger);
}

/* Links */
.auth-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
}

.auth-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Responsive */
@media (max-width: 576px) {
  .auth-container {
    padding: 1rem;
  }
  
  .auth-header {
    padding: 2rem 1.5rem;
  }
  
  .auth-form {
    padding: 1.5rem;
  }
}