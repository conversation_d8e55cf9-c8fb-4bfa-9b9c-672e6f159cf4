/* 
 * Piața.ro Premium Design System
 * Modern, Professional Marketplace CSS
 */

:root {
  /* Premium Color Palette */
  --primary-blue: #1e40af;
  --primary-light: #3b82f6;
  --primary-dark: #1e3a8a;
  --secondary-gold: #f59e0b;
  --secondary-light: #fbbf24;
  --success-green: #10b981;
  --danger-red: #ef4444;
  --warning-amber: #f59e0b;
  --info-cyan: #06b6d4;
  
  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Font Families */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-display: 'Poppins', var(--font-sans);
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Modern Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-sans);
  color: var(--gray-900);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Premium Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 700;
  line-height: 1.2;
  color: var(--gray-900);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

/* Premium Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  cursor: pointer;
  border: none;
  font-size: 1rem;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn:active::before {
  width: 300px;
  height: 300px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark));
  color: white;
  box-shadow: 0 4px 14px 0 rgba(30, 64, 175, 0.4);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 7px 20px 0 rgba(30, 64, 175, 0.5);
}

.btn-secondary {
  background: var(--gray-200);
  color: var(--gray-800);
}

.btn-secondary:hover {
  background: var(--gray-300);
}

.btn-gold {
  background: linear-gradient(135deg, var(--secondary-gold), #d97706);
  color: white;
  box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.4);
}

.btn-gold:hover {
  transform: translateY(-1px);
  box-shadow: 0 7px 20px 0 rgba(245, 158, 11, 0.5);
}

/* Premium Card Design */
.card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-blue), var(--secondary-gold));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card:hover::before {
  opacity: 1;
}

.card-body {
  padding: 1.5rem;
}

.card-header {
  padding: 1.5rem;
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

/* Premium Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control.is-invalid {
  border-color: var(--danger-red);
}

.form-control.is-invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Premium Select Dropdown */
.select-wrapper {
  position: relative;
}

.select-wrapper::after {
  content: '▼';
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  pointer-events: none;
  color: var(--gray-500);
  font-size: 0.75rem;
}

.form-select {
  appearance: none;
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 1rem;
  font-size: 1rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  background: white;
  cursor: pointer;
  transition: all var(--transition-normal);
}

/* Premium Badge Styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: var(--primary-light);
  color: white;
}

.badge-success {
  background: var(--success-green);
  color: white;
}

.badge-warning {
  background: var(--warning-amber);
  color: white;
}

.badge-danger {
  background: var(--danger-red);
  color: white;
}

.badge-featured {
  background: linear-gradient(135deg, var(--secondary-gold), #d97706);
  color: white;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

/* Premium Listing Card */
.listing-card {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
}

.listing-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.listing-card.featured {
  border: 2px solid var(--secondary-gold);
}

.listing-card.featured::before {
  content: 'PROMOVAT';
  position: absolute;
  top: 1rem;
  right: -2rem;
  background: var(--secondary-gold);
  color: white;
  padding: 0.25rem 3rem;
  font-size: 0.75rem;
  font-weight: 700;
  transform: rotate(45deg);
  box-shadow: var(--shadow-md);
  z-index: 10;
}

.listing-image-wrapper {
  position: relative;
  padding-top: 75%; /* 4:3 Aspect Ratio */
  overflow: hidden;
}

.listing-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.listing-card:hover .listing-image {
  transform: scale(1.05);
}

.listing-price {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  font-weight: 700;
  font-size: 1.25rem;
  backdrop-filter: blur(4px);
}

/* Premium Navigation */
.navbar {
  background: white;
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.navbar-brand {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-blue);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navbar-brand:hover {
  color: var(--primary-dark);
}

.nav-link {
  color: var(--gray-700);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  font-weight: 500;
}

.nav-link:hover {
  background: var(--gray-100);
  color: var(--primary-blue);
}

.nav-link.active {
  background: var(--primary-blue);
  color: white;
}

/* Premium Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-dialog {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.modal.show .modal-dialog {
  transform: scale(1);
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--gray-200);
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

/* Premium Search Box */
.search-box {
  position: relative;
  width: 100%;
  max-width: 600px;
}

.search-input {
  width: 100%;
  padding: 1rem 3rem 1rem 1.5rem;
  font-size: 1rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-full);
  background: white;
  transition: all var(--transition-normal);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.search-button {
  position: absolute;
  top: 50%;
  right: 0.5rem;
  transform: translateY(-50%);
  padding: 0.5rem 1.5rem;
  background: var(--primary-blue);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.search-button:hover {
  background: var(--primary-dark);
}

/* Premium Stats Card */
.stats-card {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark));
  color: white;
  padding: 2rem;
  border-radius: var(--radius-xl);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transform: rotate(45deg);
}

.stats-number {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  font-family: var(--font-display);
}

.stats-label {
  font-size: 1rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Premium Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top-color: var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Premium Tooltip */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-text {
  visibility: hidden;
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gray-900);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: var(--z-tooltip);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.tooltip-text::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--gray-900);
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Premium Progress Bar */
.progress {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-blue), var(--primary-light));
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255,255,255,0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255,255,255,0.2) 50%,
    rgba(255,255,255,0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 30px 30px;
  animation: progress-animation 1s linear infinite;
}

@keyframes progress-animation {
  0% { background-position: 0 0; }
  100% { background-position: 30px 30px; }
}

/* Premium Alert */
.alert {
  padding: 1rem 1.5rem;
  border-radius: var(--radius-lg);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  overflow: hidden;
}

.alert::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-green);
}

.alert-success::before {
  background: var(--success-green);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-amber);
}

.alert-warning::before {
  background: var(--warning-amber);
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-red);
}

.alert-danger::before {
  background: var(--danger-red);
}

.alert-info {
  background: rgba(6, 182, 212, 0.1);
  color: var(--info-cyan);
}

.alert-info::before {
  background: var(--info-cyan);
}

/* Premium Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn var(--transition-slow) ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn var(--transition-normal) ease-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Premium Responsive Grid */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Premium Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-900: #f9fafb;
  }
  
  body {
    background: var(--gray-900);
    color: var(--gray-50);
  }
  
  .card,
  .modal-dialog,
  .navbar {
    background: var(--gray-800);
  }
}

/* Premium Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }
}
