/* Authentication Pages Styling */

/* Primary colors */
:root {
    --primary: #0056b3;
    --primary-dark: #004494;
    --primary-light: #1a73e8;
    --primary-lighter: #4285f4;
}

/* Base styles for auth pages */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

.auth-container.login {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
}

.auth-container.signup {
    background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%);
}

.auth-card {
    width: 100%;
    max-width: 28rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    padding: 2rem;
    border: 1px solid #f3f4f6;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    font-size: 1.875rem;
    font-weight: 800;
    color: #111827;
    margin: 1.5rem 0 0.5rem 0;
}

.auth-header p {
    font-size: 0.875rem;
    color: #6b7280;
}

.auth-header .icon {
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 1rem;
}

.google-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background: white;
    color: #374151;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.google-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.google-btn svg {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
}

.divider {
    position: relative;
    margin: 1.5rem 0;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #d1d5db;
}

.divider span {
    background: white;
    padding: 0 0.75rem;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
    position: relative;
    display: block;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    color: #111827;
    background: white;
    transition: all 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

.checkbox-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
}

.checkbox-wrapper input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    accent-color: var(--primary);
}

.checkbox-wrapper label {
    font-size: 0.875rem;
    color: #111827;
}

.forgot-password {
    font-size: 0.875rem;
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
}

.forgot-password:hover {
    color: var(--primary-dark);
}

.submit-btn {
    width: 100%;
    background: var(--primary);
    color: white;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.submit-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.submit-btn i {
    position: absolute;
    left: 1rem;
}

.error-alert {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.error-alert h3 {
    color: #991b1b;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.error-alert ul {
    color: #b91c1c;
    font-size: 0.875rem;
    list-style: disc;
    padding-left: 1.25rem;
}

.error-alert li {
    margin-bottom: 0.25rem;
}

.terms-text {
    font-size: 0.75rem;
    color: #6b7280;
    text-align: center;
    margin-top: 1rem;
}

.terms-text a {
    color: var(--primary);
    text-decoration: none;
}

.terms-text a:hover {
    color: var(--primary-dark);
}

.auth-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.auth-link:hover {
    color: var(--primary-dark);
}

/* Responsive design */
@media (max-width: 640px) {
    .auth-container {
        padding: 1rem;
    }
    
    .auth-card {
        padding: 1.5rem;
    }
    
    .auth-header h2 {
        font-size: 1.5rem;
    }
}
