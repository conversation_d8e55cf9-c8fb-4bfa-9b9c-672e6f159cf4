#!/bin/bash
# Start VSCode MCP Addon Server

echo "Starting VSCode MCP Addon Server..."
echo "This server provides all VSCode extension commands as MCP tools"

# Set Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Check if required packages are installed
if ! python -c "import mcp" 2>/dev/null; then
    echo "Installing MCP package..."
    pip install mcp
fi

# Start the addon server
python ai_assistant/mcp_addon.py

echo "VSCode MCP Addon Server started successfully!"
echo "Available tools:"
echo "- vscode_analyze_file"
echo "- vscode_get_suggestions"
echo "- vscode_apply_changes"
echo "- vscode_detect_smells"
echo "- vscode_optimize_imports"
echo "- vscode_format_code"
echo "- vscode_git_hooks"
echo "- vscode_batch_analyze"
echo "- vscode_project_health"
